const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const prisma = new PrismaClient();

// Utilizatori de test pentru fiecare tip de abonament
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'Test123!',
    name: 'Test User Free',
    plan_type: 'free',
    subscription_status: null,
    monthly_expense_limit: 50,
    role: 'user'
  },
  {
    email: '<EMAIL>',
    password: 'Test123!',
    name: 'Test User Basic',
    plan_type: 'basic',
    subscription_status: 'active',
    monthly_expense_limit: 500,
    role: 'user'
  },
  {
    email: '<EMAIL>',
    password: 'Test123!',
    name: 'Test User Premium',
    plan_type: 'premium',
    subscription_status: 'active',
    monthly_expense_limit: -1, // unlimited
    role: 'user'
  }
];

// Categorii default pentru utilizatori
const defaultCategories = [
  { name: '<PERSON>âncare', icon: 'utensils', color: '#EF4444' },
  { name: 'Transport', icon: 'car', color: '#3B82F6' },
  { name: 'Util<PERSON><PERSON><PERSON><PERSON>', icon: 'home', color: '#10B981' },
  { name: 'Divertisment', icon: 'film', color: '#8B5CF6' },
  { name: 'Sănătate', icon: 'heart', color: '#F59E0B' },
  { name: 'Cumpărături', icon: 'shopping-bag', color: '#EC4899' }
];

async function createTestUsers() {
  try {
    console.log('🚀 Creating test users for each subscription type...');
    
    // Obține planurile din baza de date
    const plans = await prisma.plan.findMany({
      where: { is_active: true }
    });
    
    console.log(`📋 Found ${plans.length} active plans`);
    
    for (const userData of testUsers) {
      // Verifică dacă utilizatorul există deja
      const existingUser = await prisma.user.findUnique({
        where: { email: userData.email }
      });
      
      if (existingUser) {
        console.log(`⚠️  User ${userData.email} already exists, skipping...`);
        continue;
      }
      
      // Hash parola
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      
      // Găsește planul corespunzător
      const plan = plans.find(p => p.name.toLowerCase() === userData.plan_type);
      
      if (!plan && userData.plan_type !== 'free') {
        console.log(`❌ Plan ${userData.plan_type} not found, skipping user ${userData.email}`);
        continue;
      }
      
      // Creează utilizatorul
      console.log(`👤 Creating user: ${userData.email} (${userData.plan_type})`);
      
      const currentDate = new Date();
      const periodEnd = new Date();
      periodEnd.setMonth(periodEnd.getMonth() + 1);
      
      const user = await prisma.user.create({
        data: {
          email: userData.email,
          password: hashedPassword,
          name: userData.name,
          role: userData.role,
          plan_type: userData.plan_type,
          subscription_status: userData.subscription_status,
          monthly_expense_limit: userData.monthly_expense_limit,
          email_verified: true,
          is_active: true,
          subscription_current_period_start: userData.subscription_status ? currentDate : null,
          subscription_current_period_end: userData.subscription_status ? periodEnd : null,
          stripe_customer_id: userData.subscription_status ? `cus_test_${userData.plan_type}_${Date.now()}` : null
        }
      });
      
      console.log(`✅ User created with ID: ${user.id}`);
      
      // Creează abonamentul pentru utilizatorii cu planuri plătite
      if (plan && userData.subscription_status) {
        console.log(`📝 Creating subscription for ${userData.email}`);
        
        await prisma.subscription.create({
          data: {
            stripe_id: `sub_test_${userData.plan_type}_${Date.now()}`,
            user_id: user.id,
            plan_id: plan.id,
            status: userData.subscription_status,
            current_period_start: currentDate,
            current_period_end: periodEnd,
            metadata: {
              test_user: true,
              created_by: 'test_script'
            }
          }
        });
        
        console.log(`✅ Subscription created for ${userData.email}`);
      }
      
      // Creează categorii default pentru utilizator
      console.log(`📂 Creating default categories for ${userData.email}`);
      
      for (const [index, categoryData] of defaultCategories.entries()) {
        await prisma.category.create({
          data: {
            name: categoryData.name,
            icon: categoryData.icon,
            color: categoryData.color,
            user_id: user.id,
            is_default: true,
            sort_order: index + 1
          }
        });
      }
      
      console.log(`✅ Created ${defaultCategories.length} categories for ${userData.email}`);
      
      // Creează câteva cheltuieli de test
      console.log(`💰 Creating sample expenses for ${userData.email}`);
      
      const userCategories = await prisma.category.findMany({
        where: { user_id: user.id }
      });
      
      const sampleExpenses = [
        { amount: 25.50, description: 'Prânz la restaurant', category: 'Mâncare' },
        { amount: 15.00, description: 'Transport public', category: 'Transport' },
        { amount: 120.00, description: 'Factură electricitate', category: 'Utilități' },
        { amount: 45.00, description: 'Cinema', category: 'Divertisment' }
      ];
      
      for (const expenseData of sampleExpenses) {
        const category = userCategories.find(c => c.name === expenseData.category);
        if (category) {
          await prisma.expense.create({
            data: {
              amount: expenseData.amount,
              description: expenseData.description,
              date: new Date(),
              user_id: user.id,
              category_id: category.id,
              payment_method: 'card'
            }
          });
        }
      }
      
      console.log(`✅ Created ${sampleExpenses.length} sample expenses for ${userData.email}`);
      console.log('');
    }
    
    console.log('🎉 All test users created successfully!');
    
    // Afișează un rezumat
    console.log('\n📊 Test Users Summary:');
    console.log('='.repeat(50));
    
    for (const userData of testUsers) {
      const user = await prisma.user.findUnique({
        where: { email: userData.email },
        include: {
          subscription: {
            include: { plan: true }
          },
          _count: {
            select: {
              categories: true,
              expenses: true
            }
          }
        }
      });
      
      if (user) {
        console.log(`👤 ${user.name} (${user.email})`);
        console.log(`   Plan: ${user.plan_type}`);
        console.log(`   Status: ${user.subscription_status || 'free'}`);
        console.log(`   Categories: ${user._count.categories}`);
        console.log(`   Expenses: ${user._count.expenses}`);
        console.log(`   Password: Test123!`);
        console.log('');
      }
    }
    
    console.log('💡 You can now login with any of these test accounts!');
    
  } catch (error) {
    console.error('❌ Error creating test users:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  createTestUsers()
    .then(() => {
      console.log('🎉 Test user creation completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test user creation failed:', error);
      process.exit(1);
    });
}

module.exports = { createTestUsers };