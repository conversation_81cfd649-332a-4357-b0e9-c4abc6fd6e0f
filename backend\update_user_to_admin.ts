import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function updateUserToAdmin() {
  try {
    console.log('Conectare la baza de date...');

    // Verifică utilizatorul curent cu ID 1
    const currentUserResult = await pool.query('SELECT id, email, role, name FROM users WHERE id = $1', [1]);

    if (currentUserResult.rows.length === 0) {
      console.log('Nu există utilizator cu ID=1');
      return;
    }

    const currentUser = currentUserResult.rows[0];
    console.log('\n=== UTILIZATOR CURENT ===');
    console.log(
      `ID: ${currentUser.id}, Email: ${currentUser.email}, Role: ${currentUser.role}, Nume: ${currentUser.name}`,
    );

    // Actualizează rolul la admin
    const updateResult = await pool.query('UPDATE users SET role = $1 WHERE id = $2 RETURNING id, email, role, name', [
      'admin',
      1,
    ]);

    if (updateResult.rows.length > 0) {
      const updatedUser = updateResult.rows[0];
      console.log('\n=== UTILIZATOR ACTUALIZAT ===');
      console.log(
        `ID: ${updatedUser.id}, Email: ${updatedUser.email}, Role: ${updatedUser.role}, Nume: ${updatedUser.name}`,
      );
      console.log('\n✅ Utilizatorul a fost actualizat cu succes la rolul de admin!');
    } else {
      console.log('\n❌ Eroare la actualizarea utilizatorului');
    }
  } catch (error) {
    console.error('Eroare:', error.message);
  } finally {
    await pool.end();
  }
}

updateUserToAdmin();
