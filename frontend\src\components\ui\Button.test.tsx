import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../../tests/setup';
import Button from './Button';

describe('Button Component', () => {
  it('should render with default props', () => {
    // Arrange & Act
    render(<Button>Click me</Button>);

    // Assert
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('btn'); // assuming default class
  });

  it('should render with primary variant', () => {
    // Arrange & Act
    render(<Button variant="primary">Primary Button</Button>);

    // Assert
    const button = screen.getByRole('button', { name: /primary button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('btn-primary');
  });

  it('should render with secondary variant', () => {
    // Arrange & Act
    render(<Button variant="secondary">Secondary Button</Button>);

    // Assert
    const button = screen.getByRole('button', { name: /secondary button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('btn-secondary');
  });

  it('should render with danger variant', () => {
    // Arrange & Act
    render(<Button variant="danger">Delete</Button>);

    // Assert
    const button = screen.getByRole('button', { name: /delete/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('btn-danger');
  });

  it('should render with different sizes', () => {
    // Arrange & Act
    const { rerender } = render(<Button size="sm">Small</Button>);
    expect(screen.getByRole('button')).toHaveClass('btn-sm');

    rerender(<Button size="md">Medium</Button>);
    expect(screen.getByRole('button')).toHaveClass('btn-md');

    rerender(<Button size="lg">Large</Button>);
    expect(screen.getByRole('button')).toHaveClass('btn-lg');
  });

  it('should handle click events', () => {
    // Arrange
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    // Act
    fireEvent.click(screen.getByRole('button'));

    // Assert
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    // Arrange & Act
    render(<Button disabled>Disabled Button</Button>);

    // Assert
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('btn-disabled');
  });

  it('should not call onClick when disabled', () => {
    // Arrange
    const handleClick = vi.fn();
    render(
      <Button disabled onClick={handleClick}>
        Disabled Button
      </Button>,
    );

    // Act
    fireEvent.click(screen.getByRole('button'));

    // Assert
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('should show loading state', () => {
    // Arrange & Act
    render(<Button loading>Loading Button</Button>);

    // Assert
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('btn-loading');
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('should render with custom className', () => {
    // Arrange & Act
    render(<Button className="custom-class">Custom Button</Button>);

    // Assert
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
    expect(button).toHaveClass('btn'); // should still have base class
  });

  // Test comentat - Button nu suportă prop "as"
  // it('should render as different HTML elements when "as" prop is provided', () => {
  //   // Arrange & Act
  //   render(<Button as="a" href="/test">Link Button</Button>);

  //   // Assert
  //   const link = screen.getByRole('link');
  //   expect(link).toBeInTheDocument();
  //   expect(link).toHaveAttribute('href', '/test');
  // });

  // Test comentat - Button nu suportă prop "icon" direct
  // it('should render with icon', () => {
  //   // Arrange
  //   const TestIcon = () => <span data-testid="test-icon">Icon</span>;

  //   // Act
  //   render(<Button icon={<TestIcon />}>Button with Icon</Button>);

  //   // Assert
  //   expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  //   expect(screen.getByText('Button with Icon')).toBeInTheDocument();
  // });

  // it('should render icon only when children is not provided', () => {
  //   // Arrange
  //   const TestIcon = () => <span data-testid="test-icon">Icon</span>;

  //   // Act
  //   render(<Button icon={<TestIcon />} />);

  //   // Assert
  //   expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  //   expect(screen.getByRole('button')).toHaveClass('btn-icon-only');
  // });

  // Test comentat - Button nu suportă ref în interfața sa
  // it('should forward ref correctly', () => {
  //   // Arrange
  //   const ref = vi.fn();

  //   // Act
  //   render(<Button ref={ref}>Button with Ref</Button>);

  //   // Assert
  //   expect(ref).toHaveBeenCalledWith(expect.any(HTMLButtonElement));
  // });

  it('should handle keyboard events', () => {
    // Arrange
    const handleKeyDown = vi.fn();
    render(<Button onKeyDown={handleKeyDown}>Keyboard Button</Button>);

    // Act
    fireEvent.keyDown(screen.getByRole('button'), { key: 'Enter' });

    // Assert
    expect(handleKeyDown).toHaveBeenCalledTimes(1);
  });

  it('should have correct accessibility attributes', () => {
    // Arrange & Act
    render(
      <Button aria-label="Custom label" aria-describedby="description" type="submit">
        Submit
      </Button>,
    );

    // Assert
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Custom label');
    expect(button).toHaveAttribute('aria-describedby', 'description');
    expect(button).toHaveAttribute('type', 'submit');
  });

  it('should render full width when fullWidth prop is true', () => {
    // Arrange & Act
    render(<Button fullWidth>Full Width Button</Button>);

    // Assert
    const button = screen.getByRole('button');
    expect(button).toHaveClass('btn-full-width');
  });

  it('should apply correct styles for outline variant', () => {
    // Arrange & Act
    render(<Button variant="outline">Outline Button</Button>);

    // Assert
    const button = screen.getByRole('button');
    expect(button).toHaveClass('btn-outline');
  });

  it('should handle focus and blur events', () => {
    // Arrange
    const handleFocus = vi.fn();
    const handleBlur = vi.fn();
    render(
      <Button onFocus={handleFocus} onBlur={handleBlur}>
        Focus Button
      </Button>,
    );

    // Act
    const button = screen.getByRole('button');
    fireEvent.focus(button);
    fireEvent.blur(button);

    // Assert
    expect(handleFocus).toHaveBeenCalledTimes(1);
    expect(handleBlur).toHaveBeenCalledTimes(1);
  });
});
