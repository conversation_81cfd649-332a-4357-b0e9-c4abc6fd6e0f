import {
  CurrencyDollarIcon,
  ChartBarIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import { useQuery } from '@tanstack/react-query';
import React from 'react';

import Card from '../components/ui/Card';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { formatCurrency } from '../utils/helpers';

// Tipuri pentru pagina Dashboard
interface ExpenseByCategory {
  name: string;
  amount: number;
  percentage: number;
}

interface RecentExpense {
  id: number;
  description: string;
  amount: number;
  category: string;
  date: string;
}

interface DashboardData {
  totalExpenses: number;
  monthlyExpenses: number;
  weeklyExpenses: number;
  expensesByCategory: ExpenseByCategory[];
  recentExpenses: RecentExpense[];
}

// Mock data pentru demonstrație
const mockDashboardData: DashboardData = {
  totalExpenses: 2450.75,
  monthlyExpenses: 850.25,
  weeklyExpenses: 195.50,
  expensesByCategory: [
    { name: 'Mâncare', amount: 450.25, percentage: 53 },
    { name: 'Transport', amount: 200.00, percentage: 23 },
    { name: 'Utilități', amount: 150.00, percentage: 18 },
    { name: 'Divertisment', amount: 50.00, percentage: 6 },
  ],
  recentExpenses: [
    { id: 1, description: 'Cumpărături Carrefour', amount: 85.50, category: 'Mâncare', date: '2024-01-15' },
    { id: 2, description: 'Benzină', amount: 120.00, category: 'Transport', date: '2024-01-14' },
    { id: 3, description: 'Factură electricitate', amount: 75.25, category: 'Utilități', date: '2024-01-13' },
    { id: 4, description: 'Cinema', amount: 25.00, category: 'Divertisment', date: '2024-01-12' },
  ],
};

const Dashboard: React.FC = () => {
  // Simulare query pentru datele dashboard-ului
  const { data: dashboardData, isLoading, error } = useQuery<DashboardData>({
    queryKey: ['dashboard'],
    queryFn: async (): Promise<DashboardData> => {
      // Simulare API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return mockDashboardData;
    },
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Eroare la încărcarea datelor dashboard-ului</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Privire de ansamblu asupra cheltuielilor tale</p>
      </div>

      {/* Statistici principale */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total cheltuieli</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(dashboardData!.totalExpenses)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CalendarIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Luna aceasta</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(dashboardData!.monthlyExpenses)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Săptămâna aceasta</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(dashboardData!.weeklyExpenses)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ArrowTrendingUpIcon className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Tendință</p>
              <p className="text-2xl font-bold text-red-600">+12%</p>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cheltuieli pe categorii */}
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cheltuieli pe categorii</h3>
          <div className="space-y-4">
            {dashboardData!.expensesByCategory.map((category, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">{category.name}</span>
                    <span className="text-sm text-gray-500">{formatCurrency(category.amount)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full"
                      style={{ width: `${category.percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Cheltuieli recente */}
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cheltuieli recente</h3>
          <div className="space-y-4">
            {dashboardData!.recentExpenses.map((expense) => (
              <div key={expense.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{expense.description}</p>
                  <p className="text-xs text-gray-500">{expense.category} • {expense.date}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">{formatCurrency(expense.amount)}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
              Vezi toate cheltuielile →
            </button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
