import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  CameraIcon,
  KeyIcon,
  BellIcon,
} from '@heroicons/react/24/outline';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { z } from 'zod';

import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Input from '../components/ui/Input';
import { useAuthStore } from '../store/authStore';


// Schema pentru validarea profilului
const profileSchema = z.object({
  firstName: z.string().min(2, 'Prenumele trebuie să aibă cel puțin 2 caractere'),
  lastName: z.string().min(2, 'Numele trebuie să aibă cel puțin 2 caractere'),
  email: z.string().email('Email invalid'),
  phone: z.string().optional(),
  bio: z.string().max(500, 'Biografia nu poate depăși 500 de caractere').optional(),
});

// Schema pentru schimbarea parolei
const passwordSchema = z.object({
  currentPassword: z.string().min(1, 'Parola curentă este obligatorie'),
  newPassword: z.string().min(8, 'Parola nouă trebuie să aibă cel puțin 8 caractere'),
  confirmPassword: z.string().min(1, 'Confirmarea parolei este obligatorie'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Parolele nu se potrivesc',
  path: ['confirmPassword'],
});

// Tipuri pentru formulare
type ProfileFormData = z.infer<typeof profileSchema>;
type PasswordFormData = z.infer<typeof passwordSchema>;

const Profile: React.FC = () => {
  const { user } = useAuthStore();
  const [activeTab, setActiveTab] = useState<string>('profile');
  const [isUploading, setIsUploading] = useState<boolean>(false);

  // Form pentru profil
  const {
    register: registerProfile,
    handleSubmit: handleSubmitProfile,
    formState: { errors: profileErrors, isSubmitting: isSubmittingProfile },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      bio: user?.bio || '',
    },
  });

  // Form pentru parolă
  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    formState: { errors: passwordErrors, isSubmitting: isSubmittingPassword },
    reset: resetPasswordForm,
  } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
  });

  const handleProfileSubmit = async (data: ProfileFormData): Promise<void> => {
    try {
      // Aici ar trebui să faci request către API
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Profilul a fost actualizat cu succes!');
    } catch (error) {
      toast.error('Eroare la actualizarea profilului');
    }
  };

  const handlePasswordSubmit = async (data: PasswordFormData): Promise<void> => {
    try {
      // Aici ar trebui să faci request către API
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Parola a fost schimbată cu succes!');
      resetPasswordForm();
    } catch (error) {
      toast.error('Eroare la schimbarea parolei');
    }
  };

  const handleAvatarUpload = async (event) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Fișierul este prea mare. Dimensiunea maximă este 5MB.');
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast.error('Te rog să selectezi un fișier imagine valid.');
      return;
    }

    setIsUploading(true);
    try {
      // Aici ar trebui să faci upload către API
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Avatarul a fost actualizat cu succes!');
    } catch (error) {
      toast.error('Eroare la încărcarea avatarului');
    } finally {
      setIsUploading(false);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Informații personale', icon: UserIcon },
    { id: 'security', label: 'Securitate', icon: KeyIcon },
    { id: 'notifications', label: 'Notificări', icon: BellIcon },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Profil</h1>
        <p className="text-gray-600">Gestionează informațiile contului tău</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'profile' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Avatar */}
          <Card className="p-6">
            <div className="text-center">
              <div className="relative inline-block">
                <div className="w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center mx-auto">
                  {user?.avatar ? (
                    <img
                      src={user.avatar}
                      alt="Avatar"
                      className="w-32 h-32 rounded-full object-cover"
                    />
                  ) : (
                    <UserIcon className="h-16 w-16 text-gray-400" />
                  )}
                </div>
                <label className="absolute bottom-0 right-0 bg-primary-600 text-white p-2 rounded-full cursor-pointer hover:bg-primary-700">
                  <CameraIcon className="h-4 w-4" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    className="hidden"
                    disabled={isUploading}
                  />
                </label>
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                {user?.firstName} {user?.lastName}
              </h3>
              <p className="text-gray-500">{user?.email}</p>
              {isUploading && (
                <p className="text-sm text-primary-600 mt-2">Se încarcă...</p>
              )}
            </div>
          </Card>

          {/* Formular profil */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-6">
                Informații personale
              </h3>
              <form onSubmit={handleSubmitProfile(handleProfileSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Input
                    label="Prenume"
                    {...registerProfile('firstName')}
                    error={profileErrors.firstName?.message}
                    icon={UserIcon}
                  />
                  <Input
                    label="Nume"
                    {...registerProfile('lastName')}
                    error={profileErrors.lastName?.message}
                    icon={UserIcon}
                  />
                </div>

                <Input
                  label="Email"
                  type="email"
                  {...registerProfile('email')}
                  error={profileErrors.email?.message}
                  icon={EnvelopeIcon}
                />

                <Input
                  label="Telefon"
                  type="tel"
                  {...registerProfile('phone')}
                  error={profileErrors.phone?.message}
                  icon={PhoneIcon}
                  placeholder="+40 123 456 789"
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Biografie
                  </label>
                  <textarea
                    {...registerProfile('bio')}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Scrie câteva cuvinte despre tine..."
                  />
                  {profileErrors.bio && (
                    <p className="mt-1 text-sm text-red-600">{profileErrors.bio.message}</p>
                  )}
                </div>

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={isSubmittingProfile}
                  >
                    {isSubmittingProfile ? 'Se salvează...' : 'Salvează modificările'}
                  </Button>
                </div>
              </form>
            </Card>
          </div>
        </div>
      )}

      {activeTab === 'security' && (
        <Card className="p-6 max-w-2xl">
          <h3 className="text-lg font-medium text-gray-900 mb-6">
            Schimbă parola
          </h3>
          <form onSubmit={handleSubmitPassword(handlePasswordSubmit)} className="space-y-6">
            <Input
              label="Parola curentă"
              type="password"
              {...registerPassword('currentPassword')}
              error={passwordErrors.currentPassword?.message}
              icon={KeyIcon}
            />

            <Input
              label="Parola nouă"
              type="password"
              {...registerPassword('newPassword')}
              error={passwordErrors.newPassword?.message}
              icon={KeyIcon}
            />

            <Input
              label="Confirmă parola nouă"
              type="password"
              {...registerPassword('confirmPassword')}
              error={passwordErrors.confirmPassword?.message}
              icon={KeyIcon}
            />

            <div className="flex justify-end">
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmittingPassword}
              >
                {isSubmittingPassword ? 'Se schimbă...' : 'Schimbă parola'}
              </Button>
            </div>
          </form>
        </Card>
      )}

      {activeTab === 'notifications' && (
        <Card className="p-6 max-w-2xl">
          <h3 className="text-lg font-medium text-gray-900 mb-6">
            Preferințe notificări
          </h3>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  Notificări email
                </h4>
                <p className="text-sm text-gray-500">
                  Primește notificări despre cheltuieli și rapoarte
                </p>
              </div>
              <input
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  Notificări push
                </h4>
                <p className="text-sm text-gray-500">
                  Primește notificări în browser
                </p>
              </div>
              <input
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  Rapoarte săptămânale
                </h4>
                <p className="text-sm text-gray-500">
                  Primește un rezumat săptămânal al cheltuielilor
                </p>
              </div>
              <input
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900">
                  Alerte buget
                </h4>
                <p className="text-sm text-gray-500">
                  Primește alerte când depășești bugetul
                </p>
              </div>
              <input
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </div>

            <div className="pt-4">
              <Button variant="primary">
                Salvează preferințele
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Profile;
