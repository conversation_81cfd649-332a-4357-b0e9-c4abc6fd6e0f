import {
  CogIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  PaintBrushIcon,
  ShieldCheckIcon,
  TrashIcon,
  ArrowRightOnRectangleIcon,
  BellIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';

import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import Modal from '../components/ui/Modal';
import { useLanguage } from '../hooks/useLanguage';
import { useAuthStore } from '../store/authStore';


const Settings: React.FC = () => {
  const navigate = useNavigate();
  const { logout } = useAuthStore();
  const { t, setLanguage, currentLanguage, getLanguagesWithNames } = useLanguage();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [settings, setSettings] = useState({
    currency: 'RON',
    language: currentLanguage,
    theme: 'light',
    dateFormat: 'dd/mm/yyyy',
    notifications: {
      email: true,
      push: true,
      weekly: true,
      budget: true,
    },
    privacy: {
      dataSharing: false,
      analytics: true,
      marketing: false,
    },
  });

  const currencies = [
    { value: 'RON', label: t('currencies.RON') },
    { value: 'EUR', label: t('currencies.EUR') },
    { value: 'USD', label: t('currencies.USD') },
    { value: 'GBP', label: t('currencies.GBP') },
  ];

  const languages = getLanguagesWithNames().map(lang => ({
    value: lang.code,
    label: lang.name,
  }));

  const themes = [
    { value: 'light', label: t('settings.general.themes.light') },
    { value: 'dark', label: t('settings.general.themes.dark') },
    { value: 'auto', label: t('settings.general.themes.auto') },
  ];

  const dateFormats = [
    { value: 'dd/mm/yyyy', label: 'DD/MM/YYYY' },
    { value: 'mm/dd/yyyy', label: 'MM/DD/YYYY' },
    { value: 'yyyy-mm-dd', label: 'YYYY-MM-DD' },
  ];

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: typeof prev[category] === 'object'
        ? { ...prev[category], [key]: value }
        : value,
    }));

    // Dacă se schimbă limba, actualizează și sistemul i18n
    if (category === 'language') {
      setLanguage(value);
    }
  };

  const handleSaveSettings = async () => {
    try {
      // Aici ar trebui să faci request către API
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success(t('settings.saveSuccess'));
    } catch (error) {
      toast.error(t('settings.saveError'));
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth/login');
      toast.success(t('auth.logoutSuccess'));
    } catch (error) {
      toast.error(t('auth.loginError'));
    }
    setShowLogoutModal(false);
  };

  const handleDeleteAccount = async () => {
    try {
      // Aici ar trebui să faci request către API
      await new Promise(resolve => setTimeout(resolve, 1000));
      await logout();
      navigate('/auth/login');
      toast.success(t('settings.dangerZone.deleteAccount.success'));
    } catch (error) {
      toast.error(t('settings.dangerZone.deleteAccount.error'));
    }
    setShowDeleteModal(false);
  };

  const handleExportData = async () => {
    try {
      // Aici ar trebui să faci request către API
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success(t('settings.export.success'));
    } catch (error) {
      toast.error(t('settings.export.error'));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">{t('settings.title')}</h1>
        <p className="text-gray-600">{t('settings.subtitle')}</p>
      </div>

      {/* Setări generale */}
      <Card className="p-6">
        <div className="flex items-center mb-6">
          <CogIcon className="h-6 w-6 text-gray-400 mr-3" />
          <h2 className="text-lg font-medium text-gray-900">{t('settings.general.title')}</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('settings.general.currency')}
            </label>
            <select
              value={settings.currency}
              onChange={(e) => handleSettingChange('currency', null, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            >
              {currencies.map((currency) => (
                <option key={currency.value} value={currency.value}>
                  {currency.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('settings.general.language')}
            </label>
            <select
              value={settings.language}
              onChange={(e) => handleSettingChange('language', null, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            >
              {languages.map((language) => (
                <option key={language.value} value={language.value}>
                  {language.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('settings.general.theme')}
            </label>
            <select
              value={settings.theme}
              onChange={(e) => handleSettingChange('theme', null, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            >
              {themes.map((theme) => (
                <option key={theme.value} value={theme.value}>
                  {theme.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('settings.general.dateFormat')}
            </label>
            <select
              value={settings.dateFormat}
              onChange={(e) => handleSettingChange('dateFormat', null, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            >
              {dateFormats.map((format) => (
                <option key={format.value} value={format.value}>
                  {format.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>

      {/* Setări notificări */}
      <Card className="p-6">
        <div className="flex items-center mb-6">
          <BellIcon className="h-6 w-6 text-gray-400 mr-3" />
          <h2 className="text-lg font-medium text-gray-900">{t('settings.notifications.title')}</h2>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Notificări email</h3>
              <p className="text-sm text-gray-500">Primește notificări prin email</p>
            </div>
            <input
              type="checkbox"
              checked={settings.notifications.email}
              onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Notificări push</h3>
              <p className="text-sm text-gray-500">Primește notificări în browser</p>
            </div>
            <input
              type="checkbox"
              checked={settings.notifications.push}
              onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Rapoarte săptămânale</h3>
              <p className="text-sm text-gray-500">Primește un rezumat săptămânal</p>
            </div>
            <input
              type="checkbox"
              checked={settings.notifications.weekly}
              onChange={(e) => handleSettingChange('notifications', 'weekly', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Alerte buget</h3>
              <p className="text-sm text-gray-500">Primește alerte când depășești bugetul</p>
            </div>
            <input
              type="checkbox"
              checked={settings.notifications.budget}
              onChange={(e) => handleSettingChange('notifications', 'budget', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </Card>

      {/* Setări confidențialitate */}
      <Card className="p-6">
        <div className="flex items-center mb-6">
          <ShieldCheckIcon className="h-6 w-6 text-gray-400 mr-3" />
          <h2 className="text-lg font-medium text-gray-900">{t('settings.privacy.title')}</h2>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">{t('settings.privacy.dataSharing.title')}</h3>
              <p className="text-sm text-gray-500">{t('settings.privacy.dataSharing.description')}</p>
            </div>
            <input
              type="checkbox"
              checked={settings.privacy.dataSharing}
              onChange={(e) => handleSettingChange('privacy', 'dataSharing', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">{t('settings.privacy.analytics.title')}</h3>
              <p className="text-sm text-gray-500">{t('settings.privacy.analytics.description')}</p>
            </div>
            <input
              type="checkbox"
              checked={settings.privacy.analytics}
              onChange={(e) => handleSettingChange('privacy', 'analytics', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">{t('settings.privacy.marketing.title')}</h3>
              <p className="text-sm text-gray-500">{t('settings.privacy.marketing.description')}</p>
            </div>
            <input
              type="checkbox"
              checked={settings.privacy.marketing}
              onChange={(e) => handleSettingChange('privacy', 'marketing', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </Card>

      {/* Export date */}
      <Card className="p-6">
        <div className="flex items-center mb-6">
          <CurrencyDollarIcon className="h-6 w-6 text-gray-400 mr-3" />
          <h2 className="text-lg font-medium text-gray-900">{t('settings.export.title')}</h2>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900">{t('settings.export.allData')}</h3>
            <p className="text-sm text-gray-500">{t('settings.export.description')}</p>
          </div>
          <Button variant="outline" onClick={handleExportData}>
            {t('settings.export.button')}
          </Button>
        </div>
      </Card>

      {/* Acțiuni cont */}
      <Card className="p-6">
        <div className="flex items-center mb-6">
          <ExclamationTriangleIcon className="h-6 w-6 text-red-500 mr-3" />
          <h2 className="text-lg font-medium text-gray-900">{t('settings.dangerZone.title')}</h2>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between py-4 border-b border-gray-200">
            <div>
              <h3 className="text-sm font-medium text-gray-900">{t('settings.dangerZone.logout.title')}</h3>
              <p className="text-sm text-gray-500">{t('settings.dangerZone.logout.description')}</p>
            </div>
            <Button
              variant="outline"
              onClick={() => setShowLogoutModal(true)}
              className="flex items-center"
            >
              <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
              {t('settings.dangerZone.logout.button')}
            </Button>
          </div>

          <div className="flex items-center justify-between pt-4">
            <div>
              <h3 className="text-sm font-medium text-red-900">{t('settings.dangerZone.deleteAccount.title')}</h3>
              <p className="text-sm text-red-500">{t('settings.dangerZone.deleteAccount.description')}</p>
            </div>
            <Button
              variant="danger"
              onClick={() => setShowDeleteModal(true)}
              className="flex items-center"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              {t('settings.dangerZone.deleteAccount.button')}
            </Button>
          </div>
        </div>
      </Card>

      {/* Buton salvare */}
      <div className="flex justify-end">
        <Button variant="primary" onClick={handleSaveSettings}>
          {t('settings.saveButton')}
        </Button>
      </div>

      {/* Modal confirmare deconectare */}
      <Modal
        isOpen={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
        title={t('settings.dangerZone.logout.confirmTitle')}
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            {t('settings.dangerZone.logout.confirmMessage')}
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowLogoutModal(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button variant="primary" onClick={handleLogout}>
              {t('settings.dangerZone.logout.confirmButton')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Modal confirmare ștergere cont */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('settings.dangerZone.deleteAccount.confirmTitle')}
      >
        <div className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <TrashIcon className="h-5 w-5 text-red-400 mr-2" />
              <div>
                <h3 className="text-sm font-medium text-red-800">
                  {t('settings.dangerZone.deleteAccount.warningTitle')}
                </h3>
                <p className="text-sm text-red-700 mt-1">
                  {t('settings.dangerZone.deleteAccount.warningMessage')}
                </p>
              </div>
            </div>
          </div>

          <p className="text-gray-600">
            {t('settings.dangerZone.deleteAccount.confirmInstructions')}
          </p>

          <input
            type="text"
            placeholder={t('settings.dangerZone.deleteAccount.confirmPlaceholder')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500"
          />

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button variant="danger" onClick={handleDeleteAccount}>
              {t('settings.dangerZone.deleteAccount.confirmButton')}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Settings;
