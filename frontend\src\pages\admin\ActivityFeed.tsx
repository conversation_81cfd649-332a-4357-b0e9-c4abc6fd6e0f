import {
  UserIcon,
  CreditCardIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon,
  ArrowRightIcon,
  ClockIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';

import Badge from '../../components/ui/Badge';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import Modal from '../../components/ui/Modal';
import { useActivityFeed } from '../../hooks/useAdminData';
import { formatCurrency, formatDate } from '../../utils/helpers';

const ActivityFeed: React.FC = () => {
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [showActivityModal, setShowActivityModal] = useState(false);
  const [filter, setFilter] = useState('all');
  const [timeRange, setTimeRange] = useState('24h');

  // Query pentru activitatea recentă
  const { data: activities, isLoading, error } = useActivityFeed({
    filter,
    timeRange,
    limit: 50,
  });

  const getActivityIcon = (type) => {
    const iconMap = {
      user_registered: UserIcon,
      user_login: UserIcon,
      subscription_created: CreditCardIcon,
      subscription_canceled: XMarkIcon,
      payment_succeeded: CheckCircleIcon,
      payment_failed: ExclamationTriangleIcon,
      plan_upgraded: ArrowRightIcon,
      plan_downgraded: ArrowRightIcon,
      usage_limit_reached: ExclamationTriangleIcon,
      system_error: ExclamationTriangleIcon,
    };

    return iconMap[type] || ClockIcon;
  };

  const getActivityColor = (type) => {
    const colorMap = {
      user_registered: 'text-green-600 bg-green-100',
      user_login: 'text-blue-600 bg-blue-100',
      subscription_created: 'text-green-600 bg-green-100',
      subscription_canceled: 'text-red-600 bg-red-100',
      payment_succeeded: 'text-green-600 bg-green-100',
      payment_failed: 'text-red-600 bg-red-100',
      plan_upgraded: 'text-blue-600 bg-blue-100',
      plan_downgraded: 'text-yellow-600 bg-yellow-100',
      usage_limit_reached: 'text-yellow-600 bg-yellow-100',
      system_error: 'text-red-600 bg-red-100',
    };

    return colorMap[type] || 'text-gray-600 bg-gray-100';
  };

  const getActivityTitle = (activity) => {
    const titleMap = {
      user_registered: 'Utilizator nou înregistrat',
      user_login: 'Utilizator autentificat',
      subscription_created: 'Abonament creat',
      subscription_canceled: 'Abonament anulat',
      payment_succeeded: 'Plată reușită',
      payment_failed: 'Plată eșuată',
      plan_upgraded: 'Plan îmbunătățit',
      plan_downgraded: 'Plan retrogradat',
      usage_limit_reached: 'Limită de utilizare atinsă',
      system_error: 'Eroare de sistem',
    };

    return titleMap[activity.type] || 'Activitate necunoscută';
  };

  const getActivityDescription = (activity) => {
    const { type, data } = activity;

    switch (type) {
      case 'user_registered':
        return `${data.user?.firstName} ${data.user?.lastName} (${data.user?.email})`;
      case 'user_login':
        return `${data.user?.firstName} ${data.user?.lastName} s-a autentificat`;
      case 'subscription_created':
        return `${data.user?.firstName} ${data.user?.lastName} a creat abonament ${data.plan?.name}`;
      case 'subscription_canceled':
        return `${data.user?.firstName} ${data.user?.lastName} a anulat abonamentul ${data.plan?.name}`;
      case 'payment_succeeded':
        return `Plată de ${formatCurrency(data.amount)} pentru ${data.user?.firstName} ${data.user?.lastName}`;
      case 'payment_failed':
        return `Plată eșuată de ${formatCurrency(data.amount)} pentru ${data.user?.firstName} ${data.user?.lastName}`;
      case 'plan_upgraded':
        return `${data.user?.firstName} ${data.user?.lastName}: ${data.fromPlan} → ${data.toPlan}`;
      case 'plan_downgraded':
        return `${data.user?.firstName} ${data.user?.lastName}: ${data.fromPlan} → ${data.toPlan}`;
      case 'usage_limit_reached':
        return `${data.user?.firstName} ${data.user?.lastName} a atins limita pentru ${data.feature}`;
      case 'system_error':
        return data.message || 'Eroare de sistem';
      default:
        return activity.description || 'Fără descriere';
    }
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      high: { variant: 'error', label: 'Prioritate mare' },
      medium: { variant: 'warning', label: 'Prioritate medie' },
      low: { variant: 'secondary', label: 'Prioritate mică' },
    };

    const config = priorityConfig[priority] || priorityConfig.low;
    return <Badge variant={config.variant} size="sm">{config.label}</Badge>;
  };

  const getRelativeTime = (date) => {
    const now = new Date();
    const activityDate = new Date(date);
    const diffInMinutes = Math.floor((now - activityDate) / (1000 * 60));

    if (diffInMinutes < 1) return 'Acum';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}z`;
  };

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-600">
          Eroare la încărcarea activității
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            Activitate recentă
          </h3>
          <div className="flex gap-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="1h">Ultima oră</option>
              <option value="24h">Ultimele 24h</option>
              <option value="7d">Ultima săptămână</option>
              <option value="30d">Ultima lună</option>
            </select>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate</option>
              <option value="users">Utilizatori</option>
              <option value="payments">Plăți</option>
              <option value="subscriptions">Abonamente</option>
              <option value="errors">Erori</option>
            </select>
          </div>
        </div>

        {/* Lista de activități */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {activities?.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              Nu există activitate în perioada selectată
            </div>
          ) : (
            activities?.map((activity) => {
              const IconComponent = getActivityIcon(activity.type);
              const colorClasses = getActivityColor(activity.type);

              return (
                <div
                  key={activity.id}
                  className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={() => {
                    setSelectedActivity(activity);
                    setShowActivityModal(true);
                  }}
                >
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${colorClasses}`}>
                    <IconComponent className="h-4 w-4" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {getActivityTitle(activity)}
                      </p>
                      <div className="flex items-center space-x-2">
                        {activity.priority && activity.priority !== 'low' && (
                          getPriorityBadge(activity.priority)
                        )}
                        <span className="text-xs text-gray-500">
                          {getRelativeTime(activity.createdAt)}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 truncate">
                      {getActivityDescription(activity)}
                    </p>
                    {activity.metadata?.ip && (
                      <p className="text-xs text-gray-400">
                        IP: {activity.metadata.ip}
                      </p>
                    )}
                  </div>

                  <div className="flex-shrink-0">
                    <EyeIcon className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Statistici rapide */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-green-600">
                {activities?.filter(a => a.type.includes('succeeded') || a.type.includes('created')).length || 0}
              </div>
              <div className="text-xs text-gray-500">Acțiuni pozitive</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-red-600">
                {activities?.filter(a => a.type.includes('failed') || a.type.includes('error')).length || 0}
              </div>
              <div className="text-xs text-gray-500">Erori</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {activities?.filter(a => a.type.includes('user')).length || 0}
              </div>
              <div className="text-xs text-gray-500">Activitate utilizatori</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-purple-600">
                {activities?.filter(a => a.type.includes('payment')).length || 0}
              </div>
              <div className="text-xs text-gray-500">Tranzacții</div>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal detalii activitate */}
      <Modal
        isOpen={showActivityModal}
        onClose={() => setShowActivityModal(false)}
        title="Detalii activitate"
        size="lg"
      >
        {selectedActivity && (
          <div className="space-y-6">
            <div className="flex items-start space-x-3">
              <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                getActivityColor(selectedActivity?.type)
              }`}>
                {React.createElement(getActivityIcon(selectedActivity?.type), {
                  className: 'h-5 w-5',
                })}
              </div>
              <div className="flex-1">
                <h4 className="text-lg font-medium text-gray-900">
                  {getActivityTitle(selectedActivity)}
                </h4>
                <p className="text-sm text-gray-600">
                  {getActivityDescription(selectedActivity)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {formatDate(selectedActivity?.createdAt)}
                </p>
              </div>
              {selectedActivity?.priority && selectedActivity?.priority !== 'low' && (
                <div className="flex-shrink-0">
                  {getPriorityBadge(selectedActivity?.priority)}
                </div>
              )}
            </div>

            {/* Detalii utilizator */}
            {selectedActivity?.data?.user && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Utilizator</h5>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Nume:</span>
                    <span className="ml-2 text-gray-900">
                      {selectedActivity.data?.user?.firstName || ''} {selectedActivity.data?.user?.lastName || ''}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Email:</span>
                    <span className="ml-2 text-gray-900">{selectedActivity.data?.user?.email}</span>
                  </div>
                  {selectedActivity.data?.user?.id && (
                    <div>
                      <span className="text-gray-500">ID:</span>
                      <span className="ml-2 text-gray-900 font-mono text-xs">
                        {selectedActivity.data?.user?.id}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Detalii financiare */}
            {selectedActivity?.data?.amount && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Detalii financiare</h5>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Sumă:</span>
                    <span className="ml-2 text-gray-900 font-semibold">
                      {formatCurrency(selectedActivity?.data?.amount)}
                    </span>
                  </div>
                  {selectedActivity?.data?.currency && (
                    <div>
                      <span className="text-gray-500">Monedă:</span>
                      <span className="ml-2 text-gray-900">{selectedActivity?.data?.currency}</span>
                    </div>
                  )}
                  {selectedActivity?.data?.paymentMethod && (
                    <div>
                      <span className="text-gray-500">Metodă plată:</span>
                      <span className="ml-2 text-gray-900">{selectedActivity?.data?.paymentMethod}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Metadata tehnică */}
            {selectedActivity?.metadata && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Informații tehnice</h5>
                <div className="bg-gray-50 p-3 rounded-md">
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                    {JSON.stringify(selectedActivity?.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Detalii complete */}
            {selectedActivity?.data && Object.keys(selectedActivity?.data).length > 0 && (
              <div className="border-t pt-4">
                <h5 className="text-sm font-medium text-gray-900 mb-2">Date complete</h5>
                <div className="bg-gray-50 p-3 rounded-md max-h-40 overflow-y-auto">
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                    {JSON.stringify(selectedActivity?.data, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};

export default ActivityFeed;
