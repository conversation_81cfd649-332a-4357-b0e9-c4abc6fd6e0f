import {
  UsersIcon,
  CreditCardIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  BanknotesIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
} from '@heroicons/react/24/outline';
import React from 'react';

import Badge from '../../components/ui/Badge';
import Card from '../../components/ui/Card';
import { StatCard } from '../../components/ui/StatCard';
import { useSubscriptionStats, usePlanStats, useUsageStats } from '../../hooks/useAdminData';
import { formatCurrency, formatNumber, formatPercentage } from '../../utils/helpers';

interface AdminStatsProps {
  dashboardStats?: any;
}

const AdminStats: React.FC<AdminStatsProps> = ({ dashboardStats }) => {
  const { data: subscriptionStats } = useSubscriptionStats();
  const { data: planStats } = usePlanStats();
  const { data: usageStats } = useUsageStats();
  // Calculează metrici derivate
  const totalUsers = subscriptionStats?.totalUsers || 0;
  const activeUsers = subscriptionStats?.activeUsers || 0;
  const premiumUsers = subscriptionStats?.premiumUsers || 0;
  const freeUsers = totalUsers - premiumUsers;

  const monthlyRevenue = subscriptionStats?.monthlyRevenue || 0;
  const yearlyRevenue = subscriptionStats?.yearlyRevenue || 0;
  const averageRevenuePerUser = premiumUsers > 0 ? monthlyRevenue / premiumUsers : 0;

  const conversionRate = totalUsers > 0 ? (premiumUsers / totalUsers) * 100 : 0;
  const churnRate = subscriptionStats?.churnRate || 0;

  const totalActions = usageStats?.totalActions || 0;
  const dailyActiveUsers = usageStats?.dailyActiveUsers || 0;

  // Calculează tendințele (mock data pentru demonstrație)
  const userGrowth = subscriptionStats?.userGrowth || 5.2;
  const revenueGrowth = subscriptionStats?.revenueGrowth || 12.8;
  const usageGrowth = usageStats?.usageGrowth || 8.4;

  const StatCard = ({ title, value, icon: Icon, trend, trendValue, color = 'blue', subtitle }) => {
    const colorClasses = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      purple: 'text-purple-600',
      orange: 'text-orange-600',
      red: 'text-red-600',
      indigo: 'text-indigo-600',
    };

    const TrendIcon = trend === 'up' ? ArrowTrendingUpIcon : ArrowTrendingDownIcon;
    const trendColor = trend === 'up' ? 'text-green-600' : 'text-red-600';

    return (
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Icon className={`h-8 w-8 ${colorClasses[color]}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{title}</p>
                <p className="text-2xl font-bold text-gray-900">{value}</p>
                {subtitle && (
                  <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
                )}
              </div>
            </div>
          </div>
          {trendValue && (
            <div className="flex items-center">
              <TrendIcon className={`h-4 w-4 ${trendColor} mr-1`} />
              <span className={`text-sm font-medium ${trendColor}`}>
                {Math.abs(trendValue)}%
              </span>
            </div>
          )}
        </div>
      </Card>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total utilizatori */}
      <StatCard
        title="Total utilizatori"
        value={totalUsers}
        type="number"
        subtitle={`${activeUsers} activi astăzi`}
        icon={UsersIcon}
        trend={userGrowth}
        trendLabel="vs luna trecută"
        iconColor="blue"
      />

      {/* Venituri lunare */}
      <StatCard
        title="Venituri lunare"
        value={monthlyRevenue}
        type="currency"
        subtitle={`ARPU: ${formatCurrency(averageRevenuePerUser)}`}
        icon={BanknotesIcon}
        trend={revenueGrowth}
        trendLabel="vs luna trecută"
        iconColor="green"
      />

      {/* Utilizatori Premium */}
      <StatCard
        title="Utilizatori Premium"
        value={premiumUsers}
        type="number"
        subtitle={`${conversionRate.toFixed(1)}% conversion rate`}
        icon={CreditCardIcon}
        trend={conversionRate}
        trendLabel="conversion rate"
        iconColor="purple"
      />

      {/* Activitate zilnică */}
      <StatCard
        title="Acțiuni zilnice"
        value={totalActions}
        type="number"
        subtitle={`${dailyActiveUsers} utilizatori activi`}
        icon={ChartBarIcon}
        trend={usageGrowth}
        trendLabel="vs luna trecută"
        iconColor="indigo"
      />

      {/* Distribuția planurilor */}
      <Card className="p-6 md:col-span-2">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Distribuția planurilor
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-gray-400 rounded-full mr-3" />
              <span className="text-sm font-medium text-gray-700">Gratuit</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">{freeUsers}</span>
              <Badge variant="secondary">
                {totalUsers > 0 ? ((freeUsers / totalUsers) * 100).toFixed(1) : 0}%
              </Badge>
            </div>
          </div>

          {planStats?.plans?.map((plan, index) => {
            const planUsers = plan.userCount || 0;
            const planPercentage = totalUsers > 0 ? (planUsers / totalUsers) * 100 : 0;
            const colors = ['bg-blue-500', 'bg-purple-500', 'bg-green-500'];

            return (
              <div key={plan.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 ${colors[index % colors.length]} rounded-full mr-3`} />
                  <span className="text-sm font-medium text-gray-700">{plan.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{planUsers}</span>
                  <Badge variant="primary">
                    {planPercentage.toFixed(1)}%
                  </Badge>
                </div>
              </div>
            );
          }) || (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-purple-500 rounded-full mr-3" />
                <span className="text-sm font-medium text-gray-700">Premium</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{premiumUsers}</span>
                <Badge variant="primary">
                  {conversionRate.toFixed(1)}%
                </Badge>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Metrici cheie */}
      <Card className="p-6 md:col-span-2">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Metrici cheie
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(yearlyRevenue)}
            </p>
            <p className="text-sm text-gray-600">ARR (Annual Recurring Revenue)</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {churnRate.toFixed(1)}%
            </p>
            <p className="text-sm text-gray-600">Churn Rate</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(averageRevenuePerUser * 12)}
            </p>
            <p className="text-sm text-gray-600">LTV (Customer Lifetime Value)</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {dailyActiveUsers > 0 ? ((dailyActiveUsers / totalUsers) * 100).toFixed(1) : 0}%
            </p>
            <p className="text-sm text-gray-600">Daily Active Users</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminStats;
