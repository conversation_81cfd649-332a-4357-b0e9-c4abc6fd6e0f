import {
  CreditCardIcon,
  PauseIcon,
  PlayIcon,
  XMarkIcon,
  ArrowPathIcon,
  EllipsisVerticalIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';

import { StatusBadge as Badge } from '../../components/ui/Badge';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { ActionMenu as Dropdown } from '../../components/ui/Dropdown';
import Input from '../../components/ui/Input';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import Modal from '../../components/ui/Modal';
import { SimplePagination as Pagination } from '../../components/ui/Pagination';
import { DataTable as Table } from '../../components/ui/Table';
import {
  useSubscriptions,
  useSubscriptionDetails,
  useSuspendSubscription,
  useReactivateSubscription,
  useCancelSubscription,
  useSyncWithStripe,
} from '../../hooks/useAdminData';
import { formatCurrency } from '../../utils/helpers';

const SubscriptionManager: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [planFilter, setPlanFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedSubscription, setSelectedSubscription] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [actionType, setActionType] = useState('');

  const itemsPerPage = 10;

  // Hook-uri pentru gestionarea abonamentelor
  const { data: subscriptionsData, isLoading, error } = useSubscriptions({
    page: currentPage,
    search: searchTerm,
    status: statusFilter !== 'all' ? statusFilter : undefined,
    plan: planFilter !== 'all' ? planFilter : undefined,
    limit: itemsPerPage,
  });

  const { data: subscriptionDetails } = useSubscriptionDetails(selectedSubscription?.id, {
    enabled: !!selectedSubscription?.id,
  });

  const suspendSubscription = useSuspendSubscription();
  const reactivateSubscription = useReactivateSubscription();
  const cancelSubscription = useCancelSubscription();
  const syncWithStripe = useSyncWithStripe();

  const handleSubscriptionAction = (subscription, action) => {
    setSelectedSubscription(subscription);
    setActionType(action);
    setShowConfirmModal(true);
  };

  const confirmAction = async () => {
    if (!selectedSubscription || !actionType) return;

    try {
      switch (actionType) {
        case 'suspend':
          await suspendSubscription.mutateAsync(selectedSubscription.id);
          break;
        case 'reactivate':
          await reactivateSubscription.mutateAsync(selectedSubscription.id);
          break;
        case 'cancel':
          await cancelSubscription.mutateAsync(selectedSubscription.id);
          break;
        case 'sync':
          await syncWithStripe.mutateAsync(selectedSubscription.id);
          break;
        default:
          break;
      }
      setShowConfirmModal(false);
      setSelectedSubscription(null);
    } catch (error) {
      console.error('Action failed:', error);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { variant: 'success', label: 'Activ' },
      inactive: { variant: 'secondary', label: 'Inactiv' },
      canceled: { variant: 'error', label: 'Anulat' },
      paused: { variant: 'warning', label: 'Suspendat' },
      past_due: { variant: 'error', label: 'Întârziat' },
      trialing: { variant: 'info', label: 'Perioadă de probă' },
    };

    const config = statusConfig[status] || statusConfig.inactive;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPlanBadge = (plan) => {
    const planConfig = {
      basic: { variant: 'primary', label: 'Basic', color: 'bg-blue-100 text-blue-800' },
      premium: { variant: 'success', label: 'Premium', color: 'bg-green-100 text-green-800' },
      enterprise: { variant: 'warning', label: 'Enterprise', color: 'bg-yellow-100 text-yellow-800' },
    };

    const config = planConfig[plan?.toLowerCase()] || { variant: 'secondary', label: plan || 'Unknown' };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const columns = [
    {
      key: 'user',
      label: 'Utilizator',
      render: (subscription) => (
        <div className="flex items-center">
          <div className="ml-0">
            <div className="text-sm font-medium text-gray-900">
              {subscription.user?.firstName} {subscription.user?.lastName}
            </div>
            <div className="text-sm text-gray-500">{subscription.user?.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'plan',
      label: 'Plan',
      render: (subscription) => (
        <div>
          {getPlanBadge(subscription?.plan?.name)}
          <div className="text-xs text-gray-500 mt-1">
            {formatCurrency(subscription?.plan?.price || 0)}/lună
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (subscription) => getStatusBadge(subscription?.status),
    },
    {
      key: 'billing',
      label: 'Facturare',
      render: (subscription) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {subscription?.billingCycle === 'monthly' ? 'Lunar' : 'Anual'}
          </div>
          <div className="text-gray-500">
            Următoarea: {subscription?.nextBillingDate ?
              new Date(subscription.nextBillingDate).toLocaleDateString('ro-RO') :
              'N/A'
            }
          </div>
        </div>
      ),
    },
    {
      key: 'revenue',
      label: 'Venituri',
      render: (subscription) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {formatCurrency(subscription?.totalRevenue || 0)}
          </div>
          <div className="text-gray-500">
            {subscription?.paymentsCount || 0} plăți
          </div>
        </div>
      ),
    },
    {
      key: 'dates',
      label: 'Perioada',
      render: (subscription) => (
        <div className="text-sm">
          <div className="text-gray-900">
            Început: {subscription?.startDate ? new Date(subscription.startDate).toLocaleDateString('ro-RO') : 'N/A'}
          </div>
          {subscription?.endDate && (
            <div className="text-gray-500">
              Sfârșit: {new Date(subscription?.endDate).toLocaleDateString('ro-RO')}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Acțiuni',
      render: (subscription) => {
        const canPause = subscription?.status === 'active';
        const canResume = subscription?.status === 'paused';
        const canCancel = ['active', 'paused'].includes(subscription?.status);

        const items = [
          {
            label: 'Vezi detalii',
            icon: CreditCardIcon,
            onClick: () => {
              setSelectedSubscription(subscription);
              setShowDetailsModal(true);
            },
          },
        ];

        if (canPause) {
          items.push({
            label: 'Suspendă',
            icon: PauseIcon,
            onClick: () => handleSubscriptionAction(subscription, 'suspend'),
            className: 'text-yellow-600',
          });
        }

        if (canResume) {
          items.push({
            label: 'Reactivează',
            icon: PlayIcon,
            onClick: () => handleSubscriptionAction(subscription, 'reactivate'),
            className: 'text-green-600',
          });
        }

        if (canCancel) {
          items.push({
            label: 'Anulează',
            icon: XMarkIcon,
            onClick: () => handleSubscriptionAction(subscription, 'cancel'),
            className: 'text-red-600',
          });
        }

        items.push({
          label: 'Sincronizează cu Stripe',
          icon: ArrowPathIcon,
          onClick: () => handleSubscriptionAction(subscription, 'sync'),
        });

        return (
          <Dropdown
            trigger={
              <Button variant="ghost" size="sm">
                <EllipsisVerticalIcon className="h-4 w-4" />
              </Button>
            }
            items={items}
          />
        );
      },
    },
  ];

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-600">
          Eroare la încărcarea abonamentelor
        </div>
      </Card>
    );
  }

  const subscriptions = subscriptionsData?.subscriptions || [];
  const totalPages = Math.ceil((subscriptionsData?.total || 0) / itemsPerPage);

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            Gestionarea abonamentelor
          </h3>
          <div className="text-sm text-gray-500">
            {subscriptionsData?.total || 0} abonamente
          </div>
        </div>

        {/* Filtre */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Caută utilizatori..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate statusurile</option>
              <option value="active">Activ</option>
              <option value="paused">Suspendat</option>
              <option value="canceled">Anulat</option>
              <option value="past_due">Întârziat</option>
            </select>
            <select
              value={planFilter}
              onChange={(e) => setPlanFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate planurile</option>
              <option value="basic">Basic</option>
              <option value="premium">Premium</option>
              <option value="enterprise">Enterprise</option>
            </select>
          </div>

          {/* Statistici rapide */}
          <div className="flex gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-green-600">
                {subscriptionsData?.stats?.active || 0}
              </div>
              <div className="text-gray-500">Active</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-yellow-600">
                {subscriptionsData?.stats?.paused || 0}
              </div>
              <div className="text-gray-500">Suspendate</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-red-600">
                {subscriptionsData?.stats?.canceled || 0}
              </div>
              <div className="text-gray-500">Anulate</div>
            </div>
          </div>
        </div>

        {/* Tabel abonamente */}
        <Table
          columns={columns}
          data={subscriptions}
          emptyMessage="Nu au fost găsite abonamente"
        />

        {/* Paginare */}
        {totalPages > 1 && (
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </Card>

      {/* Modal detalii abonament */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        title="Detalii abonament"
        size="lg"
      >
        {(subscriptionDetails || selectedSubscription) && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Utilizator
                </label>
                <p className="text-sm text-gray-900">
                  {(subscriptionDetails || selectedSubscription)?.user?.firstName} {(subscriptionDetails || selectedSubscription)?.user?.lastName}
                </p>
                <p className="text-xs text-gray-500">{(subscriptionDetails || selectedSubscription)?.user?.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plan
                </label>
                <div className="flex items-center space-x-2">
                  {getPlanBadge((subscriptionDetails || selectedSubscription)?.plan?.name)}
                  <span className="text-sm text-gray-600">
                    {formatCurrency((subscriptionDetails || selectedSubscription)?.plan?.price || 0)}/lună
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                {getStatusBadge((subscriptionDetails || selectedSubscription)?.status)}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ciclu de facturare
                </label>
                <p className="text-sm text-gray-900">
                  {(subscriptionDetails || selectedSubscription)?.billingCycle === 'monthly' ? 'Lunar' : 'Anual'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data începerii
                </label>
                <p className="text-sm text-gray-900">
                  {(subscriptionDetails || selectedSubscription)?.startDate ? new Date((subscriptionDetails || selectedSubscription).startDate).toLocaleDateString('ro-RO') : 'N/A'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Următoarea facturare
                </label>
                <p className="text-sm text-gray-900">
                  {(subscriptionDetails || selectedSubscription)?.nextBillingDate ?
                    new Date((subscriptionDetails || selectedSubscription).nextBillingDate).toLocaleDateString('ro-RO') :
                    'N/A'
                  }
                </p>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Statistici financiare</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatCurrency((subscriptionDetails || selectedSubscription)?.totalRevenue || 0)}
                  </p>
                  <p className="text-xs text-gray-500">Venituri totale</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {(subscriptionDetails || selectedSubscription)?.paymentsCount || 0}
                  </p>
                  <p className="text-xs text-gray-500">Plăți efectuate</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {(subscriptionDetails || selectedSubscription)?.failedPayments || 0}
                  </p>
                  <p className="text-xs text-gray-500">Plăți eșuate</p>
                </div>
              </div>
            </div>

            {(subscriptionDetails || selectedSubscription)?.stripeSubscriptionId && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Informații Stripe</h4>
                <div className="text-sm text-gray-600">
                  <p>ID Abonament: {(subscriptionDetails || selectedSubscription)?.stripeSubscriptionId}</p>
                  {(subscriptionDetails || selectedSubscription)?.stripeCustomerId && (
                    <p>ID Client: {(subscriptionDetails || selectedSubscription)?.stripeCustomerId}</p>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Modal confirmare acțiune */}
      <Modal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title="Confirmă acțiunea"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
            <p className="text-sm text-gray-700">
              Ești sigur că vrei să {actionType === 'suspend' ? 'suspenzi' :
                actionType === 'reactivate' ? 'reactivezi' :
                  actionType === 'cancel' ? 'anulezi' :
                    actionType === 'sync' ? 'sincronizezi' : 'modifici'} acest abonament?
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="secondary"
              onClick={() => setShowConfirmModal(false)}
            >
              Anulează
            </Button>
            <Button
              variant={actionType === 'cancel' ? 'error' : 'primary'}
              onClick={confirmAction}
              disabled={suspendSubscription.isLoading || reactivateSubscription.isLoading || cancelSubscription.isLoading || syncWithStripe.isLoading}
            >
              {(suspendSubscription.isLoading || reactivateSubscription.isLoading || cancelSubscription.isLoading || syncWithStripe.isLoading) ? 'Se procesează...' : 'Confirmă'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SubscriptionManager;
