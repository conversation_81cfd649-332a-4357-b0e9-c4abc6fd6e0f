import {
  MagnifyingGlassIcon,
  UserIcon,
  EllipsisVerticalIcon,
  NoSymbolIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CreditCardIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';

import { StatusBadge as Badge } from '../../components/ui/Badge';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { ActionMenu as Dropdown } from '../../components/ui/Dropdown';
import Input from '../../components/ui/Input';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import Modal from '../../components/ui/Modal';
import { SimplePagination as Pagination } from '../../components/ui/Pagination';
import { DataTable as Table } from '../../components/ui/Table';
import UserAvatar from '../../components/ui/UserAvatar';
import {
  useUsers,
  useUserDetails,
  useBlockUser,
  useUnblockUser,
} from '../../hooks/useAdminData';
import { formatDate, formatCurrency } from '../../utils/helpers';

const UsersList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [planFilter, setPlanFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [actionType, setActionType] = useState('');

  const itemsPerPage = 10;

  // Hook-uri pentru gestionarea utilizatorilor
  const { data: usersData, isLoading, error } = useUsers({
    page: currentPage,
    search: searchTerm,
    status: statusFilter !== 'all' ? statusFilter : undefined,
    plan: planFilter !== 'all' ? planFilter : undefined,
    limit: itemsPerPage,
  });

  const { data: userDetails } = useUserDetails(selectedUser?.id, {
    enabled: !!selectedUser?.id,
  });

  const blockUser = useBlockUser();
  const unblockUser = useUnblockUser();

  const handleUserAction = (user, action) => {
    setSelectedUser(user);
    setActionType(action);
    setShowConfirmModal(true);
  };

  const confirmAction = () => {
    if (selectedUser && actionType && selectedUser?.id) {
      if (actionType === 'block') {
        blockUser.mutate(selectedUser.id);
      } else if (actionType === 'unblock') {
        unblockUser.mutate(selectedUser.id);
      }
      setShowConfirmModal(false);
      setSelectedUser(null);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { variant: 'success', label: 'Activ' },
      inactive: { variant: 'secondary', label: 'Inactiv' },
      blocked: { variant: 'error', label: 'Blocat' },
      pending: { variant: 'warning', label: 'În așteptare' },
    };

    const config = statusConfig[status] || statusConfig.inactive;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPlanBadge = (plan) => {
    const planConfig = {
      free: { variant: 'secondary', label: 'Gratuit' },
      basic: { variant: 'primary', label: 'Basic' },
      premium: { variant: 'success', label: 'Premium' },
      enterprise: { variant: 'warning', label: 'Enterprise' },
    };

    const config = planConfig[plan?.toLowerCase()] || planConfig.free;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const columns = [
    {
      key: 'user',
      label: 'Utilizator',
      render: (user) => (
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <UserAvatar 
              user={{
                name: `${user?.firstName || ''} ${user?.lastName || ''}`,
                avatar: user?.avatar,
                subscription: user?.subscription
              }} 
              size="lg" 
              showBadge={true} 
            />
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {user?.firstName || ''} {user?.lastName || ''}
            </div>
            <div className="text-sm text-gray-500">{user?.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'plan',
      label: 'Plan',
      render: (user) => getPlanBadge(user?.subscription?.plan?.name),
    },
    {
      key: 'status',
      label: 'Status',
      render: (user) => getStatusBadge(user?.status),
    },
    {
      key: 'revenue',
      label: 'Venituri',
      render: (user) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {formatCurrency(user?.totalRevenue || 0)}
          </div>
          <div className="text-gray-500">
            {user?.subscription?.plan?.price ?
              `${formatCurrency(user?.subscription?.plan?.price)}/lună` :
              'Gratuit'
            }
          </div>
        </div>
      ),
    },
    {
      key: 'joinDate',
      label: 'Data înregistrării',
      render: (user) => (
        <div className="text-sm text-gray-900">
          {user?.createdAt ? new Date(user?.createdAt).toLocaleDateString('ro-RO') : 'N/A'}
        </div>
      ),
    },
    {
      key: 'lastActive',
      label: 'Ultima activitate',
      render: (user) => (
        <div className="text-sm text-gray-500">
          {user?.lastActiveAt ?
            new Date(user?.lastActiveAt).toLocaleDateString('ro-RO') :
            'Niciodată'
          }
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Acțiuni',
      render: (user) => (
        <Dropdown
          trigger={
            <Button variant="ghost" size="sm">
              <EllipsisVerticalIcon className="h-4 w-4" />
            </Button>
          }
          items={[
            {
              label: 'Vezi detalii',
              icon: UserIcon,
              onClick: () => {
                setSelectedUser(user);
                setShowUserModal(true);
              },
            },
            {
              label: user?.status === 'blocked' ? 'Deblochează' : 'Blochează',
              icon: user?.status === 'blocked' ? CheckCircleIcon : NoSymbolIcon,
              onClick: () => handleUserAction(user, user?.status === 'blocked' ? 'unblock' : 'block'),
              className: user?.status === 'blocked' ? 'text-green-600' : 'text-red-600',
            },
            {
              label: 'Gestionează abonament',
              icon: CreditCardIcon,
              onClick: () => handleUserAction(user, 'manage-subscription'),
            },
          ]}
        />
      ),
    },
  ];

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner size="lg" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-600">
          Eroare la încărcarea utilizatorilor
        </div>
      </Card>
    );
  }

  const users = usersData?.users || [];
  const totalPages = Math.ceil((usersData?.total || 0) / itemsPerPage);

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            Gestionarea utilizatorilor
          </h3>
          <div className="text-sm text-gray-500">
            {usersData?.total || 0} utilizatori
          </div>
        </div>

        {/* Filtre și căutare */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Caută după nume sau email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              icon={MagnifyingGlassIcon}
            />
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate statusurile</option>
              <option value="active">Activ</option>
              <option value="inactive">Inactiv</option>
              <option value="blocked">Blocat</option>
            </select>
            <select
              value={planFilter}
              onChange={(e) => setPlanFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Toate planurile</option>
              <option value="free">Gratuit</option>
              <option value="basic">Basic</option>
              <option value="premium">Premium</option>
              <option value="enterprise">Enterprise</option>
            </select>
          </div>
        </div>

        {/* Tabel utilizatori */}
        <Table
          columns={columns}
          data={users}
          emptyMessage="Nu au fost găsiți utilizatori"
        />

        {/* Paginare */}
        {totalPages > 1 && (
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </Card>

      {/* Modal detalii utilizator */}
      <Modal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        title="Detalii utilizator"
        size="lg"
      >
        {selectedUser && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nume complet
                </label>
                <p className="text-sm text-gray-900">
                  {selectedUser?.name || 'N/A'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <p className="text-sm text-gray-900">{selectedUser?.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                {getStatusBadge(selectedUser?.status)}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plan curent
                </label>
                {getPlanBadge(selectedUser?.subscription?.plan?.name)}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data înregistrării
                </label>
                <p className="text-sm text-gray-900">
                  {selectedUser?.createdAt ? new Date(selectedUser?.createdAt).toLocaleDateString('ro-RO') : 'N/A'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ultima activitate
                </label>
                <p className="text-sm text-gray-900">
                  {selectedUser?.lastActiveAt ?
                    new Date(selectedUser?.lastActiveAt).toLocaleDateString('ro-RO') :
                    'Niciodată'
                  }
                </p>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Informații suplimentare</h4>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Monedă preferată
                  </label>
                  <p className="text-sm text-gray-900">{selectedUser?.currency || 'USD'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fus orar
                  </label>
                  <p className="text-sm text-gray-900">{selectedUser?.timezone || 'UTC'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email verificat
                  </label>
                  <p className="text-sm text-gray-900">
                    {selectedUser?.email_verified ? 'Da' : 'Nu'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Limită cheltuieli lunare
                  </label>
                  <p className="text-sm text-gray-900">
                    {selectedUser?.monthly_expense_count || 0} / {selectedUser?.monthly_expense_limit || 50}
                  </p>
                </div>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Statistici</h4>
              <div className="grid grid-cols-4 gap-4 text-center">
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {selectedUser?.totalExpenses || 0}
                  </p>
                  <p className="text-xs text-gray-500">Cheltuieli</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatCurrency(selectedUser?.totalExpenseAmount || 0)}
                  </p>
                  <p className="text-xs text-gray-500">Suma cheltuieli</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatCurrency(selectedUser?.totalRevenue || 0)}
                  </p>
                  <p className="text-xs text-gray-500">Venituri generate</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">
                    {selectedUser?.loginCount || 0}
                  </p>
                  <p className="text-xs text-gray-500">Autentificări</p>
                </div>
              </div>
            </div>

            {selectedUser?.subscription && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Detalii abonament</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status abonament
                    </label>
                    <p className="text-sm text-gray-900">{selectedUser?.subscription?.status || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Perioada curentă
                    </label>
                    <p className="text-sm text-gray-900">
                      {selectedUser?.subscription?.current_period_start && selectedUser?.subscription?.current_period_end ?
                        `${new Date(selectedUser.subscription.current_period_start).toLocaleDateString('ro-RO')} - ${new Date(selectedUser.subscription.current_period_end).toLocaleDateString('ro-RO')}` :
                        'N/A'
                      }
                    </p>
                  </div>
                  {selectedUser?.subscription?.trial_start && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Perioada trial
                      </label>
                      <p className="text-sm text-gray-900">
                        {`${new Date(selectedUser.subscription.trial_start).toLocaleDateString('ro-RO')} - ${new Date(selectedUser.subscription.trial_end).toLocaleDateString('ro-RO')}`}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Modal confirmare acțiune */}
      <Modal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title="Confirmă acțiunea"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600" />
            <p className="text-sm text-gray-700">
              Ești sigur că vrei să {actionType === 'block' ? 'blochezi' :
                actionType === 'unblock' ? 'deblochezi' : 'modifici'} acest utilizator?
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="secondary"
              onClick={() => setShowConfirmModal(false)}
            >
              Anulează
            </Button>
            <Button
              variant={actionType === 'block' ? 'error' : 'primary'}
              onClick={confirmAction}
              disabled={blockUser.isLoading || unblockUser.isLoading}
            >
              {(blockUser.isLoading || unblockUser.isLoading) ? 'Se procesează...' : 'Confirmă'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default UsersList;
