import {
  ArrowLeftIcon,
  MagnifyingGlassIcon,
  BookOpenIcon,
  PlayIcon,
  DocumentTextIcon,
  CodeBracketIcon,
  QuestionMarkCircleIcon,
  RocketLaunchIcon,
  CogIcon,
  ShieldCheckIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import PublicLayout from '../../components/layout/PublicLayout';

const Documentation = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    {
      id: 'all',
      name: t('docs.categories.all', 'Toate'),
      icon: BookOpenIcon,
    },
    {
      id: 'getting-started',
      name: t('docs.categories.getting_started', '<PERSON><PERSON><PERSON>'),
      icon: RocketLaunchIcon,
    },
    {
      id: 'features',
      name: t('docs.categories.features', 'Funcționalități'),
      icon: CogIcon,
    },
    {
      id: 'api',
      name: t('docs.categories.api', 'API'),
      icon: CodeBracketIcon,
    },
    {
      id: 'security',
      name: t('docs.categories.security', 'Securitate'),
      icon: ShieldCheckIcon,
    },
    {
      id: 'faq',
      name: t('docs.categories.faq', 'FAQ'),
      icon: QuestionMarkCircleIcon,
    },
  ];

  const documentationSections = [
    {
      category: 'getting-started',
      title: t('docs.getting_started.title', 'Primii Pași'),
      description: t('docs.getting_started.description', 'Totul ce trebuie să știți pentru a începe cu FinanceFlow'),
      articles: [
        {
          title: t('docs.getting_started.setup.title', 'Configurarea Contului'),
          description: t('docs.getting_started.setup.description', 'Cum să vă creați și configurați contul pentru prima dată'),
          readTime: '5 min',
          type: 'guide',
        },
        {
          title: t('docs.getting_started.first_budget.title', 'Primul Dvs. Buget'),
          description: t('docs.getting_started.first_budget.description', 'Ghid pas cu pas pentru crearea primului buget'),
          readTime: '10 min',
          type: 'tutorial',
        },
        {
          title: t('docs.getting_started.connect_bank.title', 'Conectarea Băncii'),
          description: t('docs.getting_started.connect_bank.description', 'Cum să conectați conturile bancare în siguranță'),
          readTime: '7 min',
          type: 'guide',
        },
        {
          title: t('docs.getting_started.mobile_app.title', 'Aplicația Mobilă'),
          description: t('docs.getting_started.mobile_app.description', 'Descărcarea și configurarea aplicației mobile'),
          readTime: '3 min',
          type: 'guide',
        },
      ],
    },
    {
      category: 'features',
      title: t('docs.features.title', 'Funcționalități'),
      description: t('docs.features.description', 'Ghiduri detaliate pentru toate funcționalitățile FinanceFlow'),
      articles: [
        {
          title: t('docs.features.budgeting.title', 'Bugetare Avansată'),
          description: t('docs.features.budgeting.description', 'Cum să utilizați funcționalitățile avansate de bugetare'),
          readTime: '15 min',
          type: 'guide',
        },
        {
          title: t('docs.features.analytics.title', 'Analiză și Rapoarte'),
          description: t('docs.features.analytics.description', 'Înțelegerea graficelor și rapoartelor financiare'),
          readTime: '12 min',
          type: 'guide',
        },
        {
          title: t('docs.features.goals.title', 'Obiective Financiare'),
          description: t('docs.features.goals.description', 'Setarea și urmărirea obiectivelor financiare'),
          readTime: '8 min',
          type: 'tutorial',
        },
        {
          title: t('docs.features.notifications.title', 'Notificări și Alerte'),
          description: t('docs.features.notifications.description', 'Configurarea notificărilor personalizate'),
          readTime: '6 min',
          type: 'guide',
        },
        {
          title: t('docs.features.export.title', 'Export și Backup'),
          description: t('docs.features.export.description', 'Cum să exportați și să faceți backup datelor'),
          readTime: '5 min',
          type: 'guide',
        },
      ],
    },
    {
      category: 'api',
      title: t('docs.api.title', 'API Documentation'),
      description: t('docs.api.description', 'Documentație completă pentru API-ul FinanceFlow'),
      articles: [
        {
          title: t('docs.api.authentication.title', 'Autentificare API'),
          description: t('docs.api.authentication.description', 'Cum să vă autentificați și să utilizați API-ul'),
          readTime: '10 min',
          type: 'technical',
        },
        {
          title: t('docs.api.endpoints.title', 'Endpoint-uri Disponibile'),
          description: t('docs.api.endpoints.description', 'Lista completă a endpoint-urilor API'),
          readTime: '20 min',
          type: 'reference',
        },
        {
          title: t('docs.api.webhooks.title', 'Webhooks'),
          description: t('docs.api.webhooks.description', 'Configurarea și utilizarea webhook-urilor'),
          readTime: '15 min',
          type: 'technical',
        },
        {
          title: t('docs.api.examples.title', 'Exemple de Cod'),
          description: t('docs.api.examples.description', 'Exemple practice în multiple limbaje de programare'),
          readTime: '25 min',
          type: 'tutorial',
        },
      ],
    },
    {
      category: 'security',
      title: t('docs.security.title', 'Securitate'),
      description: t('docs.security.description', 'Informații despre securitatea și protecția datelor'),
      articles: [
        {
          title: t('docs.security.overview.title', 'Prezentare Generală Securitate'),
          description: t('docs.security.overview.description', 'Cum protejăm datele și informațiile dvs.'),
          readTime: '8 min',
          type: 'guide',
        },
        {
          title: t('docs.security.2fa.title', 'Autentificare cu Doi Factori'),
          description: t('docs.security.2fa.description', 'Activarea și utilizarea 2FA pentru securitate suplimentară'),
          readTime: '5 min',
          type: 'tutorial',
        },
        {
          title: t('docs.security.privacy.title', 'Setări Confidențialitate'),
          description: t('docs.security.privacy.description', 'Gestionarea setărilor de confidențialitate'),
          readTime: '7 min',
          type: 'guide',
        },
        {
          title: t('docs.security.compliance.title', 'Conformitate și Reglementări'),
          description: t('docs.security.compliance.description', 'Standardele de conformitate pe care le respectăm'),
          readTime: '12 min',
          type: 'reference',
        },
      ],
    },
    {
      category: 'faq',
      title: t('docs.faq.title', 'Întrebări Frecvente'),
      description: t('docs.faq.description', 'Răspunsuri la cele mai comune întrebări'),
      articles: [
        {
          title: t('docs.faq.account.title', 'Întrebări despre Cont'),
          description: t('docs.faq.account.description', 'Gestionarea contului, resetarea parolei, etc.'),
          readTime: '3 min',
          type: 'faq',
        },
        {
          title: t('docs.faq.billing.title', 'Facturare și Plăți'),
          description: t('docs.faq.billing.description', 'Întrebări despre planuri, facturare și plăți'),
          readTime: '5 min',
          type: 'faq',
        },
        {
          title: t('docs.faq.technical.title', 'Probleme Tehnice'),
          description: t('docs.faq.technical.description', 'Rezolvarea problemelor tehnice comune'),
          readTime: '8 min',
          type: 'faq',
        },
        {
          title: t('docs.faq.features.title', 'Întrebări despre Funcționalități'),
          description: t('docs.faq.features.description', 'Cum să utilizați diferitele funcționalități'),
          readTime: '10 min',
          type: 'faq',
        },
      ],
    },
  ];

  const getTypeIcon = (type) => {
    switch (type) {
      case 'tutorial':
        return PlayIcon;
      case 'guide':
        return BookOpenIcon;
      case 'technical':
        return CodeBracketIcon;
      case 'reference':
        return DocumentTextIcon;
      case 'faq':
        return QuestionMarkCircleIcon;
      default:
        return DocumentTextIcon;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'tutorial':
        return 'bg-green-100 text-green-800';
      case 'guide':
        return 'bg-blue-100 text-blue-800';
      case 'technical':
        return 'bg-purple-100 text-purple-800';
      case 'reference':
        return 'bg-gray-100 text-gray-800';
      case 'faq':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredSections = documentationSections.filter(section =>
    selectedCategory === 'all' || section.category === selectedCategory,
  );

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                {t('common.back', 'Înapoi')}
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-3xl font-bold text-gray-900">
                {t('support.documentation.title', 'Documentație')}
              </h1>
            </div>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl">
              {t('support.documentation.subtitle', 'Ghiduri complete, tutoriale și documentație API pentru a vă ajuta să profitați la maximum de FinanceFlow.')}
            </p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            {/* Search Bar */}
            <div className="relative mb-6">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('docs.search.placeholder', 'Căutați în documentație...')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-800 border border-blue-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <IconComponent className="w-4 h-4 mr-2" />
                    {category.name}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Quick Start Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 mb-8 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">
                  {t('docs.quick_start.title', 'Start Rapid')}
                </h2>
                <p className="text-blue-100 mb-4">
                  {t('docs.quick_start.description', 'Nou pe FinanceFlow? Începeți cu ghidul nostru de start rapid.')}
                </p>
                <button className="bg-white text-blue-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                  {t('docs.quick_start.cta', 'Începeți Acum')}
                </button>
              </div>
              <div className="hidden md:block">
                <RocketLaunchIcon className="w-24 h-24 text-blue-200" />
              </div>
            </div>
          </div>

          {/* Documentation Sections */}
          <div className="space-y-8">
            {filteredSections.map((section, sectionIndex) => (
              <div key={sectionIndex} className="bg-white rounded-lg shadow-sm p-8">
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {section.title}
                  </h2>
                  <p className="text-gray-600">
                    {section.description}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {section.articles.map((article, articleIndex) => {
                    const TypeIcon = getTypeIcon(article.type);
                    return (
                      <div
                        key={articleIndex}
                        className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-all duration-300 hover:border-gray-300 cursor-pointer group"
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center">
                            <div className="bg-gray-100 p-2 rounded-lg mr-3">
                              <TypeIcon className="w-5 h-5 text-gray-600" />
                            </div>
                            <div>
                              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(article.type)}`}>
                                {article.type.toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <ChevronRightIcon className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                        </div>

                        <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                          {article.title}
                        </h3>

                        <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                          {article.description}
                        </p>

                        <div className="flex items-center text-sm text-gray-500">
                          <span>{article.readTime}</span>
                          <span className="mx-2">•</span>
                          <span>{t('docs.read_time', 'timp de citire')}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>

          {/* Help Section */}
          <div className="bg-gray-100 rounded-lg p-8 mt-12 text-center">
            <QuestionMarkCircleIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {t('docs.help.title', 'Nu găsiți ce căutați?')}
            </h2>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              {t('docs.help.description', 'Echipa noastră de suport este aici să vă ajute. Contactați-ne pentru asistență personalizată.')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/support/contact"
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                {t('docs.help.contact', 'Contactați Suportul')}
              </Link>
              <Link
                to="/support/community"
                className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
              >
                {t('docs.help.community', 'Comunitatea')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default Documentation;
