import {
  ArrowLeftIcon,
  MagnifyingGlassIcon,
  BookOpenIcon,
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  LightBulbIcon,
  CogIcon,
  ShieldCheckIcon,
  CreditCardIcon,
  DevicePhoneMobileIcon,
  ChevronRightIcon,
  PlayIcon,
} from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import PublicLayout from '../../components/layout/PublicLayout';

const Help = () => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    {
      id: 'all',
      name: t('help.categories.all', 'Toate'),
      icon: BookOpenIcon,
    },
    {
      id: 'getting-started',
      name: t('help.categories.getting_started', '<PERSON><PERSON><PERSON>'),
      icon: AcademicCapIcon,
    },
    {
      id: 'features',
      name: t('help.categories.features', 'Funcționalități'),
      icon: LightBulbIcon,
    },
    {
      id: 'settings',
      name: t('help.categories.settings', 'Setări'),
      icon: CogIcon,
    },
    {
      id: 'security',
      name: t('help.categories.security', 'Securitate'),
      icon: ShieldCheckIcon,
    },
    {
      id: 'billing',
      name: t('help.categories.billing', 'Facturare'),
      icon: CreditCardIcon,
    },
    {
      id: 'mobile',
      name: t('help.categories.mobile', 'Aplicația Mobilă'),
      icon: DevicePhoneMobileIcon,
    },
  ];

  const helpSections = [
    {
      title: t('help.sections.quick_start.title', 'Start Rapid'),
      description: t('help.sections.quick_start.description', 'Ghiduri pentru a începe rapid cu FinanceFlow'),
      icon: AcademicCapIcon,
      color: 'bg-blue-500',
      items: [
        {
          title: t('help.sections.quick_start.items.setup', 'Configurarea Contului'),
          description: t('help.sections.quick_start.items.setup_desc', 'Cum să vă configurați contul în 5 minute'),
          type: 'guide',
          duration: '5 min',
        },
        {
          title: t('help.sections.quick_start.items.first_transaction', 'Prima Tranzacție'),
          description: t('help.sections.quick_start.items.first_transaction_desc', 'Adăugați prima dvs. tranzacție'),
          type: 'video',
          duration: '3 min',
        },
        {
          title: t('help.sections.quick_start.items.categories', 'Organizarea Categoriilor'),
          description: t('help.sections.quick_start.items.categories_desc', 'Creați și gestionați categoriile'),
          type: 'guide',
          duration: '7 min',
        },
      ],
    },
    {
      title: t('help.sections.features.title', 'Funcționalități'),
      description: t('help.sections.features.description', 'Explorați toate funcționalitățile disponibile'),
      icon: LightBulbIcon,
      color: 'bg-green-500',
      items: [
        {
          title: t('help.sections.features.items.budgets', 'Gestionarea Bugetelor'),
          description: t('help.sections.features.items.budgets_desc', 'Creați și monitorizați bugete'),
          type: 'guide',
          duration: '10 min',
        },
        {
          title: t('help.sections.features.items.reports', 'Rapoarte și Analize'),
          description: t('help.sections.features.items.reports_desc', 'Generați rapoarte detaliate'),
          type: 'video',
          duration: '8 min',
        },
        {
          title: t('help.sections.features.items.goals', 'Obiective Financiare'),
          description: t('help.sections.features.items.goals_desc', 'Setați și urmăriți obiective'),
          type: 'guide',
          duration: '6 min',
        },
        {
          title: t('help.sections.features.items.notifications', 'Notificări Inteligente'),
          description: t('help.sections.features.items.notifications_desc', 'Configurați alertele personalizate'),
          type: 'guide',
          duration: '4 min',
        },
      ],
    },
    {
      title: t('help.sections.troubleshooting.title', 'Rezolvarea Problemelor'),
      description: t('help.sections.troubleshooting.description', 'Soluții pentru problemele comune'),
      icon: QuestionMarkCircleIcon,
      color: 'bg-yellow-500',
      items: [
        {
          title: t('help.sections.troubleshooting.items.sync', 'Probleme de Sincronizare'),
          description: t('help.sections.troubleshooting.items.sync_desc', 'Rezolvați problemele de sincronizare'),
          type: 'guide',
          duration: '5 min',
        },
        {
          title: t('help.sections.troubleshooting.items.login', 'Probleme de Autentificare'),
          description: t('help.sections.troubleshooting.items.login_desc', 'Nu vă puteți conecta la cont?'),
          type: 'guide',
          duration: '3 min',
        },
        {
          title: t('help.sections.troubleshooting.items.performance', 'Performanță Aplicație'),
          description: t('help.sections.troubleshooting.items.performance_desc', 'Aplicația funcționează lent?'),
          type: 'guide',
          duration: '4 min',
        },
      ],
    },
    {
      title: t('help.sections.advanced.title', 'Funcții Avansate'),
      description: t('help.sections.advanced.description', 'Pentru utilizatorii experimentați'),
      icon: CogIcon,
      color: 'bg-purple-500',
      items: [
        {
          title: t('help.sections.advanced.items.api', 'Integrare API'),
          description: t('help.sections.advanced.items.api_desc', 'Conectați aplicații externe'),
          type: 'documentation',
          duration: '15 min',
        },
        {
          title: t('help.sections.advanced.items.automation', 'Automatizări'),
          description: t('help.sections.advanced.items.automation_desc', 'Configurați reguli automate'),
          type: 'guide',
          duration: '12 min',
        },
        {
          title: t('help.sections.advanced.items.export', 'Export Date'),
          description: t('help.sections.advanced.items.export_desc', 'Exportați datele în diverse formate'),
          type: 'guide',
          duration: '6 min',
        },
      ],
    },
  ];

  const popularArticles = [
    {
      title: t('help.popular.article1.title', 'Cum să configurez primul meu buget?'),
      views: '2.5k',
      category: 'getting-started',
    },
    {
      title: t('help.popular.article2.title', 'Conectarea conturilor bancare'),
      views: '1.8k',
      category: 'features',
    },
    {
      title: t('help.popular.article3.title', 'Securitatea datelor financiare'),
      views: '1.2k',
      category: 'security',
    },
    {
      title: t('help.popular.article4.title', 'Utilizarea aplicației mobile'),
      views: '980',
      category: 'mobile',
    },
    {
      title: t('help.popular.article5.title', 'Gestionarea abonamentelor'),
      views: '756',
      category: 'billing',
    },
  ];

  const getTypeIcon = (type) => {
    switch (type) {
      case 'video':
        return VideoCameraIcon;
      case 'documentation':
        return DocumentTextIcon;
      default:
        return BookOpenIcon;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'video':
        return 'text-red-600 bg-red-50';
      case 'documentation':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-green-600 bg-green-50';
    }
  };

  const filteredSections = helpSections.filter(section => {
    if (selectedCategory === 'all') return true;
    return section.items.some(item =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  });

  return (
    <PublicLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center space-x-4">
              <Link
                to="/"
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2" />
                {t('common.back', 'Înapoi')}
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-3xl font-bold text-gray-900">
                {t('support.help.title', 'Centrul de Ajutor')}
              </h1>
            </div>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl">
              {t('support.help.subtitle', 'Găsiți răspunsuri la întrebările dvs. și învățați să folosiți FinanceFlow la maximum.')}
            </p>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Search and Categories */}
          <div className="mb-12">
            {/* Search Bar */}
            <div className="relative max-w-2xl mx-auto mb-8">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                placeholder={t('help.search.placeholder', 'Căutați în centrul de ajutor...')}
              />
            </div>

            {/* Categories */}
            <div className="flex flex-wrap justify-center gap-3">
              {categories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                    }`}
                  >
                    <IconComponent className="w-4 h-4 mr-2" />
                    {category.name}
                  </button>
                );
              })}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Help Sections */}
              <div className="space-y-8">
                {filteredSections.map((section, sectionIndex) => {
                  const SectionIcon = section.icon;
                  return (
                    <div key={sectionIndex} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                      <div className="p-6 border-b border-gray-200">
                        <div className="flex items-center">
                          <div className={`${section.color} p-3 rounded-lg mr-4`}>
                            <SectionIcon className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h2 className="text-xl font-semibold text-gray-900">
                              {section.title}
                            </h2>
                            <p className="text-gray-600 mt-1">
                              {section.description}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {section.items.map((item, itemIndex) => {
                            const TypeIcon = getTypeIcon(item.type);
                            return (
                              <div key={itemIndex} className="group cursor-pointer">
                                <div className="flex items-start p-4 rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all">
                                  <div className={`p-2 rounded-lg mr-4 ${getTypeColor(item.type)}`}>
                                    <TypeIcon className="w-5 h-5" />
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-center justify-between">
                                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                        {item.title}
                                      </h3>
                                      <ChevronRightIcon className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                                    </div>
                                    <p className="text-gray-600 text-sm mt-1">
                                      {item.description}
                                    </p>
                                    <div className="flex items-center mt-2">
                                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                        {item.duration}
                                      </span>
                                      {item.type === 'video' && (
                                        <PlayIcon className="w-3 h-3 text-red-500 ml-2" />
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              {/* Popular Articles */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {t('help.popular.title', 'Articole Populare')}
                </h3>
                <div className="space-y-3">
                  {popularArticles.map((article, index) => (
                    <div key={index} className="group cursor-pointer">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                            {article.title}
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">
                            {article.views} {t('help.popular.views', 'vizualizări')}
                          </p>
                        </div>
                        <ChevronRightIcon className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors ml-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {t('help.quick_actions.title', 'Acțiuni Rapide')}
                </h3>
                <div className="space-y-3">
                  <Link
                    to="/support/contact"
                    className="flex items-center p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <ChatBubbleLeftRightIcon className="w-5 h-5 text-gray-400 group-hover:text-blue-600 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 group-hover:text-blue-600">
                        {t('help.quick_actions.contact', 'Contactați Suportul')}
                      </p>
                      <p className="text-xs text-gray-500">
                        {t('help.quick_actions.contact_desc', 'Obțineți ajutor personalizat')}
                      </p>
                    </div>
                  </Link>

                  <Link
                    to="/support/documentation"
                    className="flex items-center p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <DocumentTextIcon className="w-5 h-5 text-gray-400 group-hover:text-blue-600 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 group-hover:text-blue-600">
                        {t('help.quick_actions.docs', 'Documentația API')}
                      </p>
                      <p className="text-xs text-gray-500">
                        {t('help.quick_actions.docs_desc', 'Pentru dezvoltatori')}
                      </p>
                    </div>
                  </Link>

                  <button className="flex items-center p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors group w-full text-left">
                    <VideoCameraIcon className="w-5 h-5 text-gray-400 group-hover:text-blue-600 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900 group-hover:text-blue-600">
                        {t('help.quick_actions.video_tour', 'Tur Video')}
                      </p>
                      <p className="text-xs text-gray-500">
                        {t('help.quick_actions.video_tour_desc', 'Prezentare generală')}
                      </p>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-white mb-4">
              {t('help.cta.title', 'Nu ați găsit ceea ce căutați?')}
            </h2>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              {t('help.cta.description', 'Echipa noastră de suport este gata să vă ajute cu orice întrebare sau problemă.')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/support/contact"
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
              >
                {t('help.cta.contact', 'Contactați Suportul')}
              </Link>
              <button className="bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-800 transition-colors">
                {t('help.cta.chat', 'Chat Live')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default Help;
