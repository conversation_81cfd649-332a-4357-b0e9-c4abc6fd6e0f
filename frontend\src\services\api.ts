import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Configurația de bază pentru API
const API_BASE_URL =
  typeof process !== 'undefined' && process.env && process.env.REACT_APP_API_URL
    ? process.env.REACT_APP_API_URL
    : 'http://localhost:3000/api';

// Creează instanța axios
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor pentru request - adaugă token-ul de autentificare
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('accessToken');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError): Promise<AxiosError> => {
    return Promise.reject(error);
  },
);

// Interceptor pentru response - gestionează erorile globale
api.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    return response;
  },
  (error: AxiosError): Promise<AxiosError> => {
    // Gestionează erorile de autentificare
    if (error.response?.status === 401) {
      // Token expirat sau invalid
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    // Gestionează erorile de autorizare
    if (error.response?.status === 403) {
      // Acces interzis
      console.error('Acces interzis:', (error.response.data as any)?.message);
    }

    // Gestionează erorile de server
    if (error.response?.status && error.response.status >= 500) {
      console.error('Eroare de server:', (error.response.data as any)?.message);
    }

    return Promise.reject(error);
  },
);

export default api;
