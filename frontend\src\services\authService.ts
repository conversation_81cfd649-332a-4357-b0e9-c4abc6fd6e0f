import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

import { API_BASE_URL, API_ENDPOINTS } from '../utils/constants';
import type {
  User,
  LoginCredentials,
  RegisterData,
  UpdateProfileData,
  ChangePasswordData,
  ApiResponse,
} from '../types';

// Tipuri pentru serviciul de autentificare
interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

interface AuthResponse {
  success: boolean;
  data?: {
    user: User;
    accessToken: string;
    refreshToken: string;
  };
  message?: string;
  errors?: Record<string, string[]> | null;
}

interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  exp: number;
}

interface UserFromToken {
  id: string;
  email: string;
  role: string;
}

// Configurare axios instance pentru autentificare
const authAPI: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Variabilă pentru stocarea token-ului curent
let currentToken: string | null = null;

// Interceptor pentru request-uri - adaugă token-ul de autentificare
authAPI.interceptors.request.use(
  config => {
    // Folosește token-ul setat prin setAuthToken în loc de localStorage direct
    if (currentToken && config.headers) {
      config.headers.Authorization = `Bearer ${currentToken}`;
    }
    return config;
  },
  (error: AxiosError): Promise<AxiosError> => {
    return Promise.reject(error);
  },
);

// Interceptor pentru response-uri - gestionează erorile de autentificare
authAPI.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    return response;
  },
  async (error: AxiosError): Promise<AxiosError> => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Dacă primim 401 și nu am încercat deja să reîmprospătăm token-ul
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      console.log('🔄 Attempting token refresh due to 401 error');

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          console.log('🔑 Found refresh token, attempting refresh...');

          // Apelează direct funcția de refresh din authService
          const refreshResponse = await authAPI.post(API_ENDPOINTS.AUTH.REFRESH, {
            refreshToken,
          });

          if (refreshResponse.data.success) {
            const { tokens } = refreshResponse.data.data;
            const { accessToken, refreshToken: newRefreshToken } = tokens;

            console.log('✅ Token refresh successful');

            // Actualizează token-urile
            currentToken = accessToken;
            authAPI.defaults.headers.Authorization = `Bearer ${accessToken}`;
            localStorage.setItem('accessToken', accessToken);
            if (newRefreshToken) {
              localStorage.setItem('refreshToken', newRefreshToken);
            }

            // Retrimite cererea originală cu noul token
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            }
            return authAPI(originalRequest);
          } else {
            console.log('❌ Token refresh failed - invalid response');
          }
        } else {
          console.log('❌ No refresh token found');
        }
      } catch (refreshError: any) {
        console.log(
          '❌ Token refresh error:',
          refreshError.response?.data?.message || refreshError.message,
        );

        // Refresh token invalid, curăță token-urile și redirecționează
        currentToken = null;
        delete authAPI.defaults.headers.Authorization;
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');

        // Evită redirecționarea dacă suntem deja pe pagina de login
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }
        return Promise.reject(refreshError);
      }

      // Dacă nu s-a putut reîmprospăta, curăță token-urile
      console.log('❌ Token refresh failed - clearing auth state');
      currentToken = null;
      delete authAPI.defaults.headers.Authorization;
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');

      // Evită redirecționarea dacă suntem deja pe pagina de login
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  },
);

// Serviciul de autentificare
export const authService = {
  // Setează token-ul de autentificare
  setAuthToken: (token: string | null): void => {
    if (token) {
      currentToken = token;
      authAPI.defaults.headers.Authorization = `Bearer ${token}`;
      localStorage.setItem('accessToken', token);
    } else {
      currentToken = null;
      delete authAPI.defaults.headers.Authorization;
      localStorage.removeItem('accessToken');
    }
  },

  // Șterge token-ul de autentificare
  clearAuthToken: (): void => {
    currentToken = null;
    delete authAPI.defaults.headers.Authorization;
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  },

  // Înregistrare utilizator nou
  register: async (userData: RegisterData): Promise<AuthResponse> => {
    try {
      const response = await authAPI.post(API_ENDPOINTS.AUTH.REGISTER, {
        name: userData.name,
        email: userData.email,
        password: userData.password,
        confirmPassword: userData.password, // Assuming confirmPassword should match password
      });

      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la înregistrare',
        errors: error.response?.data?.errors || null,
      };
    }
  },

  // Autentificare utilizator
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      const response = await authAPI.post(API_ENDPOINTS.AUTH.LOGIN, {
        email: credentials.email,
        password: credentials.password,
      });

      const { user, tokens } = response.data.data;
      const { accessToken, refreshToken } = tokens;

      // Salvează token-urile în localStorage
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);

      // Setează token-ul pentru cererile viitoare
      authService.setAuthToken(accessToken);

      return {
        success: true,
        data: { user, accessToken, refreshToken },
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la autentificare',
        errors: error.response?.data?.errors || null,
      };
    }
  },

  // Deconectare utilizator
  logout: async (refreshToken: string): Promise<{ success: boolean; message: string }> => {
    try {
      await authAPI.post(API_ENDPOINTS.AUTH.LOGOUT, {
        refreshToken,
      });

      authService.clearAuthToken();

      return {
        success: true,
        message: 'Deconectare reușită',
      };
    } catch (error: any) {
      // Chiar dacă logout-ul de pe server eșuează, curățăm token-urile locale
      authService.clearAuthToken();

      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la deconectare',
      };
    }
  },

  // Reîmprospătează token-ul de acces
  refreshToken: async (
    refreshToken: string,
  ): Promise<{
    success: boolean;
    data?: { accessToken: string; refreshToken: string };
    message?: string;
  }> => {
    try {
      const response = await authAPI.post(API_ENDPOINTS.AUTH.REFRESH, {
        refreshToken,
      });

      const { tokens } = response.data.data;
      const { accessToken, refreshToken: newRefreshToken } = tokens;

      // Actualizează token-urile folosind setAuthToken pentru consistență
      authService.setAuthToken(accessToken);
      if (newRefreshToken) {
        localStorage.setItem('refreshToken', newRefreshToken);
      }

      return {
        success: true,
        data: { accessToken, refreshToken: newRefreshToken || refreshToken },
        message: response.data.message,
      };
    } catch (error: any) {
      authService.clearAuthToken();

      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la reîmprospătarea token-ului',
      };
    }
  },

  // Obține profilul utilizatorului curent
  getProfile: async (): Promise<{ success: boolean; data?: User; message?: string }> => {
    try {
      const response = await authAPI.get(API_ENDPOINTS.AUTH.PROFILE);

      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la obținerea profilului',
      };
    }
  },

  // Actualizează profilul utilizatorului
  updateProfile: async (
    profileData: UpdateProfileData,
  ): Promise<{
    success: boolean;
    data?: User;
    message?: string;
    errors?: Record<string, string[]> | null;
  }> => {
    try {
      const response = await authAPI.put(API_ENDPOINTS.AUTH.PROFILE, profileData);

      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la actualizarea profilului',
        errors: error.response?.data?.errors || null,
      };
    }
  },

  // Schimbă parola utilizatorului
  changePassword: async (
    passwordData: ChangePasswordData,
  ): Promise<{
    success: boolean;
    message?: string;
    errors?: Record<string, string[]> | null;
  }> => {
    try {
      const response = await authAPI.put(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, {
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
        confirmPassword: passwordData.confirmPassword,
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la schimbarea parolei',
        errors: error.response?.data?.errors || null,
      };
    }
  },

  // Trimite email pentru resetarea parolei
  forgotPassword: async (
    email: string,
  ): Promise<{
    success: boolean;
    message?: string;
    errors?: Record<string, string[]> | null;
  }> => {
    try {
      const response = await authAPI.post(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, {
        email,
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la trimiterea email-ului',
        errors: error.response?.data?.errors || null,
      };
    }
  },

  // Resetează parola cu token-ul primit pe email
  resetPassword: async (
    token: string,
    newPassword: string,
  ): Promise<{
    success: boolean;
    message?: string;
    errors?: Record<string, string[]> | null;
  }> => {
    try {
      const response = await authAPI.post(API_ENDPOINTS.AUTH.RESET_PASSWORD, {
        token,
        password: newPassword,
        confirmPassword: newPassword,
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la resetarea parolei',
        errors: error.response?.data?.errors || null,
      };
    }
  },

  // Verifică email-ul utilizatorului
  verifyEmail: async (token: string): Promise<{ success: boolean; message?: string }> => {
    try {
      const response = await authAPI.post(API_ENDPOINTS.AUTH.VERIFY_EMAIL, {
        token,
      });

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la verificarea email-ului',
      };
    }
  },

  // Retrimite email-ul de verificare
  resendVerificationEmail: async (): Promise<{ success: boolean; message?: string }> => {
    try {
      const response = await authAPI.post(API_ENDPOINTS.AUTH.RESEND_VERIFICATION);

      return {
        success: true,
        message: response.data.message,
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Eroare la retrimiterea email-ului',
      };
    }
  },

  // Verifică dacă utilizatorul este autentificat
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('accessToken');
    return !!token;
  },

  // Obține token-ul de acces din localStorage
  getAccessToken: (): string | null => {
    return localStorage.getItem('accessToken');
  },

  // Obține refresh token-ul din localStorage
  getRefreshToken: (): string | null => {
    return localStorage.getItem('refreshToken');
  },

  // Verifică dacă token-ul a expirat
  isTokenExpired: (token: string | null): boolean => {
    if (!token) return true;

    try {
      // Decodează payload-ul JWT fără verificarea semnăturii
      const parts = token.split('.');
      if (parts.length !== 3) return true;

      const payload: TokenPayload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      // Verifică dacă token-ul a expirat (cu o marjă de 30 de secunde)
      return payload.exp < currentTime + 30;
    } catch (error) {
      console.error('Error decoding token:', error);
      return true; // Consideră token-ul expirat dacă nu poate fi decodat
    }
  },

  // Obține informații despre utilizator din token
  getUserFromToken: (token: string | null): UserFromToken | null => {
    if (!token) return null;

    try {
      const parts = token.split('.');
      if (parts.length !== 3) return null;

      const payload: TokenPayload = JSON.parse(atob(parts[1]));
      return {
        id: payload.userId,
        email: payload.email,
        role: payload.role,
      };
    } catch (error) {
      return null;
    }
  },
};

export default authService;
