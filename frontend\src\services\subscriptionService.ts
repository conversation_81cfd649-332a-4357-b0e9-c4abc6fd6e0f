import api from './api';
import type { SubscriptionPlan, ApiResponse } from '../types';

// Tipuri pentru serviciul de abonamente
interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'canceled' | 'past_due' | 'incomplete' | 'trialing';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  stripeSubscriptionId: string;
  plan: SubscriptionPlan;
  createdAt: string;
  updatedAt: string;
}

interface CheckoutSession {
  id: string;
  url: string;
  status: 'open' | 'complete' | 'expired';
  planId: string;
  billingCycle: 'monthly' | 'yearly';
}

interface CustomerPortal {
  url: string;
}

interface UsageStats {
  currentPeriodUsage: {
    expenses: number;
    categories: number;
    exports: number;
  };
  limits: {
    expenses: number;
    categories: number;
    exports: number;
  };
  resetDate: string;
}

interface Permission {
  action: string;
  allowed: boolean;
  reason?: string;
  upgradeRequired?: boolean;
}

/**
 * Serviciu pentru gestionarea abonamentelor utilizatorilor finali
 */
class SubscriptionService {
  /**
   * Obține planurile disponibile
   */
  async getPlans(): Promise<ApiResponse<SubscriptionPlan[]>> {
    try {
      const response = await api.get('/subscriptions/plans');
      return response.data;
    } catch (error) {
      console.error('Eroare la obținerea planurilor:', error);
      throw error;
    }
  }

  /**
   * Obține abonamentul curent al utilizatorului
   */
  async getCurrentSubscription(): Promise<ApiResponse<Subscription | null>> {
    try {
      const response = await api.get('/subscriptions/current');
      return response.data;
    } catch (error) {
      console.error('Eroare la obținerea abonamentului curent:', error);
      throw error;
    }
  }

  /**
   * Creează o sesiune de checkout pentru un plan
   */
  async createCheckoutSession(
    planId: string,
    billingCycle: 'monthly' | 'yearly' = 'monthly',
    successUrl: string,
    cancelUrl: string
  ): Promise<ApiResponse<CheckoutSession>> {
    try {
      const response = await api.post('/subscriptions/checkout', {
        planId,
        billingCycle,
        successUrl,
        cancelUrl
      });
      return response.data;
    } catch (error) {
      console.error('Eroare la crearea sesiunii de checkout:', error);
      throw error;
    }
  }

  /**
   * Creează un portal pentru gestionarea abonamentului
   */
  async createCustomerPortal(returnUrl: string): Promise<ApiResponse<CustomerPortal>> {
    try {
      const response = await api.post('/subscriptions/portal', {
        returnUrl
      });
      return response.data;
    } catch (error) {
      console.error('Eroare la crearea portalului de clienți:', error);
      throw error;
    }
  }

  /**
   * Anulează abonamentul curent
   */
  async cancelSubscription(reason: string = ''): Promise<ApiResponse<Subscription>> {
    try {
      const response = await api.post('/subscriptions/cancel', {
        reason
      });
      return response.data;
    } catch (error) {
      console.error('Eroare la anularea abonamentului:', error);
      throw error;
    }
  }

  /**
   * Reactivează un abonament anulat
   */
  async reactivateSubscription(): Promise<ApiResponse<Subscription>> {
    try {
      const response = await api.post('/subscriptions/reactivate');
      return response.data;
    } catch (error) {
      console.error('Eroare la reactivarea abonamentului:', error);
      throw error;
    }
  }

  /**
   * Obține statisticile de utilizare ale utilizatorului
   */
  async getUsageStats(): Promise<ApiResponse<UsageStats>> {
    try {
      const response = await api.get('/subscriptions/usage');
      return response.data;
    } catch (error) {
      console.error('Eroare la obținerea statisticilor de utilizare:', error);
      throw error;
    }
  }

  /**
   * Verifică statusul unei sesiuni de checkout
   */
  async checkCheckoutSession(sessionId: string): Promise<ApiResponse<CheckoutSession>> {
    try {
      const response = await api.get(`/subscriptions/checkout/${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Eroare la verificarea sesiunii de checkout:', error);
      throw error;
    }
  }

  /**
   * Verifică permisiunile utilizatorului pentru o anumită acțiune
   */
  async checkPermission(action: string): Promise<ApiResponse<Permission>> {
    try {
      const response = await api.get(`/subscriptions/permissions/${action}`);
      return response.data;
    } catch (error) {
      console.error('Eroare la verificarea permisiunilor:', error);
      throw error;
    }
  }
}

const subscriptionService = new SubscriptionService();
export default subscriptionService;