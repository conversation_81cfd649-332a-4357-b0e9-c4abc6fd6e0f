// Tipuri centrale pentru aplicația Expense Tracker

// Tipuri de bază
export interface BaseEntity {
  id: string | number;
  created_at: string;
  updated_at: string;
}

// Tipuri pentru utilizatori
export interface User extends BaseEntity {
  email: string;
  name: string;
  avatar?: string;
  role: 'user' | 'admin';
  subscription_plan: 'free' | 'basic' | 'premium';
  subscription_status: 'active' | 'inactive' | 'canceled' | 'past_due';
  last_login?: string;
  email_verified: boolean;
  preferences: UserPreferences;
}

export interface UserPreferences {
  language: 'ro' | 'en';
  currency: 'RON' | 'EUR' | 'USD' | 'GBP';
  date_format: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    push: boolean;
    weekly_reports: boolean;
    monthly_reports: boolean;
  };
}

// Tipuri pentru categorii
export interface Category extends BaseEntity {
  name: string;
  description?: string;
  icon: string;
  color: string;
  is_default: boolean;
  user_id?: number;
  expense_count?: number;
  total_amount?: number;
}

// Tipuri pentru cheltuieli
export interface Expense extends BaseEntity {
  amount: number;
  description: string;
  notes?: string;
  expense_date: string;
  payment_method: 'cash' | 'card' | 'bank_transfer' | 'digital_wallet' | 'check' | 'other';
  location?: string;
  tags: string[];
  category_id: number;
  category?: Category;
  user_id: number;
  receipt_url?: string;
  is_recurring: boolean;
  recurring_frequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  recurring_end_date?: string;
}

// Tipuri pentru formulare
export interface CreateExpenseForm {
  amount: number;
  description: string;
  notes?: string;
  expense_date: string;
  payment_method: string;
  location?: string;
  tags: string[];
  category_id: number;
  is_recurring?: boolean;
  recurring_frequency?: string;
  recurring_end_date?: string;
}

export interface UpdateExpenseForm extends Partial<CreateExpenseForm> {
  id: string | number;
}

export interface CreateCategoryForm {
  name: string;
  description?: string;
  icon: string;
  color: string;
}

export interface UpdateCategoryForm extends Partial<CreateCategoryForm> {
  id: string | number;
}

// Tipuri pentru autentificare
export interface LoginForm {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  terms_accepted: boolean;
}

export interface AuthResponse {
  success: boolean;
  data: {
    user: User;
    access_token: string;
    refresh_token: string;
    expires_in: number;
  };
  message?: string;
}

// Tipuri pentru abonamente
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  limits: {
    expenses_per_month: number;
    categories: number;
    exports_per_month: number;
  };
  stripe_price_id: string;
  is_popular?: boolean;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface ExpenseFormData {
  amount: number;
  description: string;
  categoryId: string;
  date: string;
}

export interface CategoryFormData {
  name: string;
  color: string;
  icon: string;
}

// Chart types
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string[];
    borderWidth?: number;
  }[];
}

// Store types
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface UpdateProfileData {
  name?: string;
  email?: string;
  avatar?: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Component prop types
export interface ExpenseCardProps {
  expense: Expense;
  onEdit?: (expense: Expense) => void;
  onDelete?: (id: string) => void;
}

export interface CategoryBadgeProps {
  category: Category;
  size?: 'sm' | 'md' | 'lg';
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

// Filter and sort types
export interface ExpenseFilters {
  categoryId?: string;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface SortOptions {
  field: 'date' | 'amount' | 'description';
  direction: 'asc' | 'desc';
}

// Re-export tipuri din constants
export type { PaymentMethod, RecurringFrequency } from '../utils/constants';
export type { Currency, Language } from '../utils/constants';

// Statistics types
export interface ExpenseStats {
  totalExpenses: number;
  monthlyTotal: number;
  categoryBreakdown: {
    categoryId: string;
    categoryName: string;
    total: number;
    percentage: number;
  }[];
  monthlyTrend: {
    month: string;
    total: number;
  }[];
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Language și Currency types sunt importate din constants

// Settings types
export interface UserSettings {
  theme: Theme;
  language: Language;
  currency: Currency;
  notifications: {
    email: boolean;
    push: boolean;
    weekly: boolean;
    monthly: boolean;
  };
}
