import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

import { CURRENCIES, CURRENCY_SYMBOLS, PAYMENT_METHOD_LABELS } from './constants';

// Tipuri pentru funcțiile helper
interface FormatCurrencyOptions {
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  showSymbol?: boolean;
  locale?: string;
}

interface FormatDateOptions {
  format?: 'short' | 'medium' | 'long' | 'full';
  includeTime?: boolean;
  locale?: string;
}

type DateInput = Date | string | null | undefined;

/**
 * Combină și optimizează clasele CSS folosind clsx și tailwind-merge
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

/**
 * Formatează o sumă de bani cu simbolul valuței
 */
export function formatCurrency(
  amount: number,
  currency: string = 'RON',
  options: FormatCurrencyOptions = {},
): string {
  const {
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    showSymbol = true,
    locale = 'ro-RO',
  } = options;

  if (typeof amount !== 'number' || isNaN(amount)) {
    return '0,00';
  }

  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits,
    maximumFractionDigits,
  });

  const formattedAmount = formatter.format(Math.abs(amount));
  const symbol = showSymbol
    ? CURRENCY_SYMBOLS[currency as keyof typeof CURRENCY_SYMBOLS] || currency
    : '';
  const sign = amount < 0 ? '-' : '';

  if (currency === 'RON') {
    return `${sign}${formattedAmount} ${symbol}`;
  }

  return `${sign}${symbol}${formattedAmount}`;
}

/**
 * Formatează o dată în format românesc
 */
export function formatDate(date: DateInput, options: FormatDateOptions = {}): string {
  const {
    format = 'short', // 'short', 'medium', 'long', 'full'
    includeTime = false,
    locale = 'ro-RO',
  } = options;

  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (isNaN(dateObj.getTime())) {
    return 'Dată invalidă';
  }

  const formatOptions: Record<string, Intl.DateTimeFormatOptions> = {
    short: {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    },
    medium: {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    },
    long: {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    },
    full: {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    },
  };

  let dateFormatOptions = formatOptions[format] || formatOptions.short;

  if (includeTime) {
    dateFormatOptions = {
      ...dateFormatOptions,
      hour: '2-digit',
      minute: '2-digit',
    };
  }

  return new Intl.DateTimeFormat(locale, dateFormatOptions).format(dateObj);
}

/**
 * Formatează o dată relativă (acum, acum 2 ore, ieri, etc.)
 */
export function formatRelativeDate(date: DateInput, locale: string = 'ro-RO'): string {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (isNaN(dateObj.getTime())) {
    return 'Dată invalidă';
  }

  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInSeconds < 60) {
    return 'acum';
  }

  if (diffInMinutes < 60) {
    return `acum ${diffInMinutes} ${diffInMinutes === 1 ? 'minut' : 'minute'}`;
  }

  if (diffInHours < 24) {
    return `acum ${diffInHours} ${diffInHours === 1 ? 'oră' : 'ore'}`;
  }

  if (diffInDays === 1) {
    return 'ieri';
  }

  if (diffInDays < 7) {
    return `acum ${diffInDays} zile`;
  }

  if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `acum ${weeks} ${weeks === 1 ? 'săptămână' : 'săptămâni'}`;
  }

  if (diffInDays < 365) {
    const months = Math.floor(diffInDays / 30);
    return `acum ${months} ${months === 1 ? 'lună' : 'luni'}`;
  }

  const years = Math.floor(diffInDays / 365);
  return `acum ${years} ${years === 1 ? 'an' : 'ani'}`;
}

/**
 * Calculează numărul de zile între două date
 */
export function daysBetween(startDate: DateInput, endDate: DateInput): number {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;

  if (!start || !end) return 0;

  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Verifică dacă o dată este în trecut
 */
export function isPastDate(date: DateInput): boolean {
  if (!date) return false;

  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj < today;
}

/**
 * Verifică dacă o dată este astăzi
 */
export function isToday(date: DateInput): boolean {
  if (!date) return false;

  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const today = new Date();
  return (
    dateObj.getDate() === today.getDate() &&
    dateObj.getMonth() === today.getMonth() &&
    dateObj.getFullYear() === today.getFullYear()
  );
}

/**
 * Formatează un număr cu separatori de mii
 */
export function formatNumber(number: number, locale: string = 'ro-RO'): string {
  if (typeof number !== 'number' || isNaN(number)) {
    return '0';
  }

  return new Intl.NumberFormat(locale).format(number);
}

/**
 * Formatează un procent
 */
export function formatPercentage(value: number, total: number, decimals: number = 1): string {
  if (!total || total === 0) return '0%';

  const percentage = (value / total) * 100;
  return `${percentage.toFixed(decimals)}%`;
}

/**
 * Scurtează un text la o lungime specificată
 */
export function truncateText(
  text: string | null | undefined,
  maxLength: number,
  suffix: string = '...',
): string {
  if (!text || text.length <= maxLength) {
    return text || '';
  }

  return text.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * Capitalizează prima literă a unui string
 */
export function capitalize(str: string | null | undefined): string {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convertește un string în format kebab-case
 */
export function kebabCase(str: string | null | undefined): string {
  if (!str) return '';
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

/**
 * Convertește un string în format camelCase
 */
export function camelCase(str: string | null | undefined): string {
  if (!str) return '';
  return str
    .replace(/[-_\s]+(.)?/g, (_, char) => (char ? char.toUpperCase() : ''))
    .replace(/^[A-Z]/, char => char.toLowerCase());
}

/**
 * Generează un ID unic
 */
export function generateId(prefix: string = 'id'): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `${prefix}_${timestamp}_${randomStr}`;
}

/**
 * Validează o adresă de email
 */
export function isValidEmail(email: string | null | undefined): boolean {
  if (!email) return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validează un număr de telefon românesc
 */
export function isValidPhoneNumber(phone: string | null | undefined): boolean {
  if (!phone) return false;
  const phoneRegex = /^(\+40|0040|0)[2-9]\d{8}$/;
  return phoneRegex.test(phone.replace(/[\s-]/g, ''));
}

/**
 * Debounce pentru funcții
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate: boolean = false,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null;
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

/**
 * Throttle pentru funcții
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number,
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Copiază text în clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback pentru browsere mai vechi
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      textArea.remove();
      return result;
    }
  } catch (error) {
    console.error('Eroare la copierea în clipboard:', error);
    return false;
  }
}

/**
 * Descarcă un fișier
 */
export function downloadFile(
  data: string | Blob,
  filename: string,
  type: string = 'text/plain',
): void {
  const blob = data instanceof Blob ? data : new Blob([data], { type });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
}

/**
 * Formatează dimensiunea unui fișier
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}

/**
 * Obține culoarea pentru o categorie bazată pe numele acesteia
 */
export function getCategoryColor(categoryName: string | null | undefined): string {
  const colors = [
    'bg-blue-100 text-blue-800',
    'bg-green-100 text-green-800',
    'bg-yellow-100 text-yellow-800',
    'bg-red-100 text-red-800',
    'bg-purple-100 text-purple-800',
    'bg-pink-100 text-pink-800',
    'bg-indigo-100 text-indigo-800',
    'bg-gray-100 text-gray-800',
  ];

  if (!categoryName) return colors[0];

  // Generează un index bazat pe numele categoriei
  let hash = 0;
  for (let i = 0; i < categoryName.length; i++) {
    hash = categoryName.charCodeAt(i) + ((hash << 5) - hash);
  }

  const colorIndex = Math.abs(hash) % colors.length;
  return colors[colorIndex];
}

/**
 * Formatează metoda de plată
 */
export function formatPaymentMethod(paymentMethod: string): string {
  return (
    PAYMENT_METHOD_LABELS[paymentMethod as keyof typeof PAYMENT_METHOD_LABELS] || paymentMethod
  );
}

/**
 * Verifică dacă un obiect este gol
 */
export function isEmpty(obj: any): boolean {
  if (obj == null) return true;
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
  return Object.keys(obj).length === 0;
}

/**
 * Sortează un array de obiecte după o proprietate
 */
export function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {
  return [...array].sort((a, b) => {
    const aVal = a[key];
    const bVal = b[key];

    if (aVal < bVal) return direction === 'asc' ? -1 : 1;
    if (aVal > bVal) return direction === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * Grupează un array de obiecte după o proprietate
 */
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((groups: Record<string, T[]>, item: T) => {
    const group = String(item[key]);
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {});
}
